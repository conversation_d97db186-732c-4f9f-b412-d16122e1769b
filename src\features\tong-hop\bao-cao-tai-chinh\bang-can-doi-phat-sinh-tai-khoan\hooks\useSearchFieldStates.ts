import { useState } from 'react';

export interface SearchFieldStates {
  account: any | null;
  setAccount: (account: any | null) => void;
  getSearchFieldData: () => any;
}

export const useSearchFieldStates = (): SearchFieldStates => {
  const [account, setAccount] = useState<any | null>(null);

  const getSearchFieldData = () => {
    return {
      tk: account?.uuid || null
    };
  };

  return {
    account,
    setAccount,
    getSearchFieldData
  };
};
