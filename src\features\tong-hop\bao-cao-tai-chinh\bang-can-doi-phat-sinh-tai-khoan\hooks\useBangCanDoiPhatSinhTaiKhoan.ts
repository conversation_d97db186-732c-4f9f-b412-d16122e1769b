import { useState, useCallback } from 'react';
import { TrialBalanceItem, TrialBalanceResponse, SearchFormValues, UseTrialBalanceReturn } from '../schema';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

/**
 * Custom hook for managing Trial Balance data
 *
 * This hook provides functionality to fetch trial balance data
 * with mock support for testing and development purposes.
 */
export function useTrialBalanceData(searchParams: SearchFormValues): UseTrialBalanceReturn {
  const { entity } = useAuth();
  const [data, setData] = useState<TrialBalanceItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (searchParams: SearchFormValues) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<TrialBalanceResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BANG_CAN_DOI_PHAT_SINH_TAI_KHOAN}/`,
          searchParams
        );

        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          // Mark first row for bold styling
          const processedData = response.data.results.map((item: any, index: number) => ({
            ...item,
            isFirstRow: index === 0
          }));
          setData(processedData);
        } else {
          setData([]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
