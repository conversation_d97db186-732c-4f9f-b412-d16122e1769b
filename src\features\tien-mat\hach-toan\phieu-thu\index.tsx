'use client';

import { useEffect, useState } from 'react';
import Split from 'react-split';
import {
  useFormState as useFormHandler,
  useSearchState,
  useDataTables,
  useSearchFieldStates,
  usePhieuThuDetail
} from './hooks';
import { SearchDialog, ActionBar, PhieuThuForm, InputTableActionBar } from './components';
import { AritoDataTables, LoadingOverlay, InputTable } from '@/components/custom/arito';
import { PhieuThu, PhieuThuChiTiet } from '@/types/schemas';
import { getInputTableColumns } from './cols-definition';
import { useRows, useFormState } from '@/hooks';

export default function PhieuThuPage() {
  const [inputDetails, setInputDetails] = useState<PhieuThuChiTiet[]>([]);
  const { selectedObj, handleRowClick, clearSelection, selectedRowIndex } = useRows<PhieuThu>();
  const { handleViewClick } = useFormState();
  const { searchDialogOpen, setSearchDialogOpen, setSearchFilters } = useSearchState();
  const { addPhieuThu, updatePhieuThu, deletePhieuThu, refreshPhieuThus, tables, isLoading } =
    useDataTables(handleViewClick);
  const {
    showForm,
    setShowForm,
    formMode,
    handleFormSubmit,
    handleAddClick,
    handleEditClick,
    handleCopyClick,
    handleDeleteClick,
    handleSearchClick,
    handleSearchClose,
    handleSearchSubmit,
    handleRefreshClick,
    handlePrintClick,
    handleFixedColumnsClick,
    handleMultiPrintClick,
    handleExportDataClick,
    handleDownloadExcelTemplateClick,
    handleImportFromExcelClick
  } = useFormHandler({
    selectedObj,
    clearSelection,
    addPhieuThu,
    updatePhieuThu,
    deletePhieuThu,
    refreshPhieuThus,
    setSearchDialogOpen,
    setSearchFilters
  });
  const searchFieldStates = useSearchFieldStates(selectedObj);
  const { detail, fetchDetail } = usePhieuThuDetail(selectedObj?.uuid);

  useEffect(() => {
    if (selectedObj) {
      setInputDetails(selectedObj.child_data || []);
    } else {
      setInputDetails([]);
    }
  }, [selectedObj]);

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {showForm && (
          <PhieuThuForm
            formMode={formMode}
            selectedObj={selectedObj}
            inputDetails={inputDetails}
            setInputDetails={setInputDetails}
            searchFieldStates={searchFieldStates}
            onSubmit={handleFormSubmit}
            onClose={() => setShowForm(false)}
          />
        )}

        {!showForm && (
          <>
            <SearchDialog open={searchDialogOpen} onClose={handleSearchClose} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onPrintClick={handlePrintClick}
              onSearchClick={handleSearchClick}
              onRefreshClick={async () => {
                await handleRefreshClick();
                await fetchDetail();
              }}
              onFixedColumnsClick={handleFixedColumnsClick}
              onMultiPrintClick={handleMultiPrintClick}
              onExportDataClick={handleExportDataClick}
              onDownloadExcelTemplateClick={handleDownloadExcelTemplateClick}
              onImportFromExcelClick={handleImportFromExcelClick}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
