/**
 * TypeScript interfaces for PhieuKeToanTheoNghiepVu (Accounting Voucher by Business Operation) models
 *
 * This file contains interfaces for the PhieuKeToanTheoNghiepVu model and its related models
 * from the Django backend, including details and tax information.
 */

import { ApiResponse } from '../api.type';

/**
 * Main PhieuKeToanTheoNghiepVu interface
 */
export interface PhieuKeToanTheoNghiepVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Account code associated with this voucher
   */
  ma_ngv: string;

  /**
   * Entity unit ID (nullable)
   */
  unit_id?: string | null;

  /**
   * Document authority ID (nullable)
   */
  ma_nk?: string | null;

  /**
   * Currency ID (nullable)
   */
  ma_nt?: string | null;

  /**
   * Internal document number
   */
  i_so_ct: string;

  /**
   * Document number reference (nullable)
   */
  so_ct?: string | null;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Last document date
   */
  ngay_lct: string;

  /**
   * Description of the voucher
   */
  dien_giai: string;

  /**
   * Exchange rate (default: 1)
   */
  ty_gia: number;

  /**
   * Status of the voucher (default: "5")
   */
  status: string;

  /**
   * Transfer flag (default: false)
   */
  transfer_yn: boolean;

  /**
   * Total amount in foreign currency
   */
  t_ps_nt: number;

  /**
   * Total amount in base currency
   */
  t_ps: number;

  /**
   * Progress ID (default: 0)
   */
  id_progress: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;

  /**
   * Related details (optional, populated when needed)
   */
  chi_tiet?: ChiTietPhieuKeToanTheoNghiepVu[];

  /**
   * Related tax information (optional, populated when needed)
   */
  thong_tin_thue?: ThuePhieuKeToanTheoNghiepVu[];

  /**
   * Related details data (read-only, from API response)
   */
  chi_tiet_data?: ChiTietPhieuKeToanTheoNghiepVu[];

  /**
   * Related tax information data (read-only, from API response)
   */
  thong_tin_thue_data?: ThuePhieuKeToanTheoNghiepVu[];
}

/**
 * ChiTietPhieuKeToanTheoNghiepVu (Detail) interface
 */
export interface ChiTietPhieuKeToanTheoNghiepVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Parent voucher UUID
   */
  phieu_ke_toan_theo_nghiep_vu: string;

  /**
   * Line number
   */
  line: number;

  /**
   * Accounting voucher code
   */
  ma_ngvkt?: string;

  /**
   * Debit account ID
   */
  tk_no: string;

  /**
   * Credit account ID
   */
  tk_co: string;

  /**
   * Subsidiary debit account ID (nullable)
   */
  tk_cn_no?: string | null;

  /**
   * Subsidiary credit account ID (nullable)
   */
  tk_cn_co?: string | null;

  /**
   * Customer ID (nullable)
   */
  ma_kh?: string | null;

  /**
   * Credit customer ID (nullable)
   */
  ma_kh_co?: string | null;

  /**
   * Secondary exchange rate (default: 0)
   */
  ty_gia2: number;

  /**
   * Amount in foreign currency
   */
  ps_nt: number;

  /**
   * Amount in base currency
   */
  ps: number;

  /**
   * Description of this detail line
   */
  dien_giai?: string;

  /**
   * Original document reference (nullable)
   */
  so_ct0?: string | null;

  /**
   * Original document date (nullable)
   */
  ngay_ct0?: string | null;

  /**
   * Department ID (nullable)
   */
  ma_bp?: string | null;

  /**
   * Case/matter ID (nullable)
   */
  ma_vv?: string | null;

  /**
   * Contract ID (nullable)
   */
  ma_hd?: string | null;

  /**
   * Payment period ID (nullable)
   */
  ma_dtt?: string | null;

  /**
   * Agreement ID (nullable)
   */
  ma_ku?: string | null;

  /**
   * Fee ID (nullable)
   */
  ma_phi?: string | null;

  /**
   * Product ID (nullable)
   */
  ma_sp?: string | null;

  /**
   * Production order code
   */
  ma_lsx?: string;

  /**
   * Invalid expense ID (nullable)
   */
  ma_cp0?: string | null;

  /**
   * Payment ID
   */
  id_tt?: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * ThuePhieuKeToanTheoNghiepVu (Tax Information) interface
 */
export interface ThuePhieuKeToanTheoNghiepVu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Parent voucher UUID
   */
  phieu_ke_toan_theo_nghiep_vu: string;

  /**
   * Line number
   */
  line: number;

  /**
   * Original document reference (nullable)
   */
  so_ct0?: string | null;

  /**
   * Secondary document reference (nullable)
   */
  so_ct2?: string | null;

  /**
   * Original document date (nullable)
   */
  ngay_ct0?: string | null;

  /**
   * Tax code ID (nullable)
   */
  ma_thue?: string | null;

  /**
   * Tax rate
   */
  thue_suat: number;

  /**
   * Document template code
   */
  ma_mau_ct?: string;

  /**
   * Report template code
   */
  ma_mau_bc?: string;

  /**
   * Tax calculation code
   */
  ma_tc_thue?: string;

  /**
   * Customer ID (nullable)
   */
  ma_kh?: string | null;

  /**
   * Tax customer name
   */
  ten_kh_thue?: string;

  /**
   * Address
   */
  dia_chi?: string;

  /**
   * Tax identification number
   */
  ma_so_thue?: string;

  /**
   * Tax item name
   */
  ten_vt_thue?: string;

  /**
   * Total amount in foreign currency (default: 0)
   */
  t_tien_nt: number;

  /**
   * Total amount in base currency (default: 0)
   */
  t_tien: number;

  /**
   * Total tax in foreign currency (default: 0)
   */
  t_thue_nt: number;

  /**
   * Total tax in base currency (default: 0)
   */
  t_thue: number;

  /**
   * Tax debit account ID (nullable)
   */
  tk_thue_no?: string | null;

  /**
   * Tax debit account name
   */
  ten_tk_thue_no?: string;

  /**
   * Balance account ID (nullable)
   */
  tk_du?: string | null;

  /**
   * Balance account name
   */
  ten_tk_du?: string;

  /**
   * Secondary customer ID (nullable)
   */
  ma_kh9?: string | null;

  /**
   * Secondary customer name
   */
  ten_kh9?: string;

  /**
   * Payment terms ID (nullable)
   */
  ma_tt?: string | null;

  /**
   * Payment terms name
   */
  ten_tt?: string;

  /**
   * Notes
   */
  ghi_chu?: string;

  /**
   * Payment terms ID (nullable)
   */
  id_tt?: string | null;

  /**
   * Department ID (nullable)
   */
  ma_bp?: string | null;

  /**
   * Case/matter ID (nullable)
   */
  ma_vv?: string | null;

  /**
   * Contract ID (nullable)
   */
  ma_hd?: string | null;

  /**
   * Payment period ID (nullable)
   */
  ma_dtt?: string | null;

  /**
   * Agreement ID (nullable)
   */
  ma_ku?: string | null;

  /**
   * Fee ID (nullable)
   */
  ma_phi?: string | null;

  /**
   * Product ID (nullable)
   */
  ma_sp?: string | null;

  /**
   * Production order code
   */
  ma_lsx?: string;

  /**
   * Invalid expense ID (nullable)
   */
  ma_cp0?: string | null;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Input type for creating/updating PhieuKeToanTheoNghiepVu
 */
export interface PhieuKeToanTheoNghiepVuInput {
  /**
   * Account code associated with this voucher
   */
  ma_ngv: string;

  /**
   * Entity unit ID (optional)
   */
  unit_id?: string | null;

  /**
   * Document authority ID (optional)
   */
  ma_nk?: string | null;

  /**
   * Currency ID (optional)
   */
  ma_nt?: string | null;

  /**
   * Internal document number
   */
  i_so_ct: string;

  /**
   * Document number reference (optional)
   */
  so_ct?: string | null;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Last document date
   */
  ngay_lct: string;

  /**
   * Description of the voucher
   */
  dien_giai: string;

  /**
   * Exchange rate (default: 1)
   */
  ty_gia?: number;

  /**
   * Status of the voucher (default: "5")
   */
  status?: string;

  /**
   * Transfer flag (default: false)
   */
  transfer_yn?: boolean;

  /**
   * Total amount in foreign currency
   */
  t_ps_nt: number;

  /**
   * Total amount in base currency
   */
  t_ps: number;

  /**
   * Progress ID (default: 0)
   */
  id_progress?: number;

  /**
   * Related details (optional)
   */
  chi_tiet?: ChiTietPhieuKeToanTheoNghiepVuInput[];

  /**
   * Related tax information (optional)
   */
  thong_tin_thue?: ThuePhieuKeToanTheoNghiepVuInput[];
}

/**
 * Input type for creating/updating ChiTietPhieuKeToanTheoNghiepVu
 */
export interface ChiTietPhieuKeToanTheoNghiepVuInput {
  /**
   * Line number
   */
  line: number;

  /**
   * Accounting voucher code (optional)
   */
  ma_ngvkt?: string;

  /**
   * Debit account ID
   */
  tk_no: string;

  /**
   * Credit account ID
   */
  tk_co: string;

  /**
   * Subsidiary debit account ID (optional)
   */
  tk_cn_no?: string | null;

  /**
   * Subsidiary credit account ID (optional)
   */
  tk_cn_co?: string | null;

  /**
   * Customer ID (optional)
   */
  ma_kh?: string | null;

  /**
   * Credit customer ID (optional)
   */
  ma_kh_co?: string | null;

  /**
   * Secondary exchange rate (default: 0)
   */
  ty_gia2?: number;

  /**
   * Amount in foreign currency
   */
  ps_nt: number;

  /**
   * Amount in base currency
   */
  ps: number;

  /**
   * Description of this detail line (optional)
   */
  dien_giai?: string;

  /**
   * Original document reference (optional)
   */
  so_ct0?: string | null;

  /**
   * Original document date (optional)
   */
  ngay_ct0?: string | null;

  /**
   * Department ID (optional)
   */
  ma_bp?: string | null;

  /**
   * Case/matter ID (optional)
   */
  ma_vv?: string | null;

  /**
   * Contract ID (optional)
   */
  ma_hd?: string | null;

  /**
   * Payment period ID (optional)
   */
  ma_dtt?: string | null;

  /**
   * Agreement ID (optional)
   */
  ma_ku?: string | null;

  /**
   * Fee ID (optional)
   */
  ma_phi?: string | null;

  /**
   * Product ID (optional)
   */
  ma_sp?: string | null;

  /**
   * Production order code (optional)
   */
  ma_lsx?: string;

  /**
   * Invalid expense ID (optional)
   */
  ma_cp0?: string | null;

  /**
   * Payment ID (optional)
   */
  id_tt?: string;
}

/**
 * Input type for creating/updating ThuePhieuKeToanTheoNghiepVu
 */
export interface ThuePhieuKeToanTheoNghiepVuInput {
  /**
   * Line number
   */
  line: number;

  /**
   * Original document reference (optional)
   */
  so_ct0?: string | null;

  /**
   * Secondary document reference (optional)
   */
  so_ct2?: string | null;

  /**
   * Original document date (optional)
   */
  ngay_ct0?: string | null;

  /**
   * Tax code ID (optional)
   */
  ma_thue?: string | null;

  /**
   * Tax rate
   */
  thue_suat: number;

  /**
   * Document template code (optional)
   */
  ma_mau_ct?: string;

  /**
   * Report template code (optional)
   */
  ma_mau_bc?: string;

  /**
   * Tax calculation code (optional)
   */
  ma_tc_thue?: string;

  /**
   * Customer ID (optional)
   */
  ma_kh?: string | null;

  /**
   * Tax customer name (optional)
   */
  ten_kh_thue?: string;

  /**
   * Address (optional)
   */
  dia_chi?: string;

  /**
   * Tax identification number (optional)
   */
  ma_so_thue?: string;

  /**
   * Tax item name (optional)
   */
  ten_vt_thue?: string;

  /**
   * Total amount in foreign currency (default: 0)
   */
  t_tien_nt?: number;

  /**
   * Total amount in base currency (default: 0)
   */
  t_tien?: number;

  /**
   * Total tax in foreign currency (default: 0)
   */
  t_thue_nt?: number;

  /**
   * Total tax in base currency (default: 0)
   */
  t_thue?: number;

  /**
   * Tax debit account ID (optional)
   */
  tk_thue_no?: string | null;

  /**
   * Tax debit account name (optional)
   */
  ten_tk_thue_no?: string;

  /**
   * Balance account ID (optional)
   */
  tk_du?: string | null;

  /**
   * Balance account name (optional)
   */
  ten_tk_du?: string;

  /**
   * Secondary customer ID (optional)
   */
  ma_kh9?: string | null;

  /**
   * Secondary customer name (optional)
   */
  ten_kh9?: string;

  /**
   * Payment terms ID (optional)
   */
  ma_tt?: string | null;

  /**
   * Payment terms name (optional)
   */
  ten_tt?: string;

  /**
   * Notes (optional)
   */
  ghi_chu?: string;

  /**
   * Payment terms ID (optional)
   */
  id_tt?: string | null;

  /**
   * Department ID (optional)
   */
  ma_bp?: string | null;

  /**
   * Case/matter ID (optional)
   */
  ma_vv?: string | null;

  /**
   * Contract ID (optional)
   */
  ma_hd?: string | null;

  /**
   * Payment period ID (optional)
   */
  ma_dtt?: string | null;

  /**
   * Agreement ID (optional)
   */
  ma_ku?: string | null;

  /**
   * Fee ID (optional)
   */
  ma_phi?: string | null;

  /**
   * Product ID (optional)
   */
  ma_sp?: string | null;

  /**
   * Production order code (optional)
   */
  ma_lsx?: string;

  /**
   * Invalid expense ID (optional)
   */
  ma_cp0?: string | null;
}

/**
 * API Response types
 */
export type PhieuKeToanTheoNghiepVuResponse = ApiResponse<PhieuKeToanTheoNghiepVu>;
export type ChiTietPhieuKeToanTheoNghiepVuResponse = ApiResponse<ChiTietPhieuKeToanTheoNghiepVu>;
export type ThuePhieuKeToanTheoNghiepVuResponse = ApiResponse<ThuePhieuKeToanTheoNghiepVu>;
