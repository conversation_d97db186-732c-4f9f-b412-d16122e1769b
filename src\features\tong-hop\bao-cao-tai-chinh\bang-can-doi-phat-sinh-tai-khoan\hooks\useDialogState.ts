import { useState } from 'react';
import { SearchFormValues } from '../schema';

interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showEditPrintDialog: boolean;
  showData: boolean;
  searchParams: SearchFormValues | null;

  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchFormValues) => void;
  handleSearchClick: () => void;
  openEditPrintDialog: () => void;
  closeEditPrintDialog: () => void;

  handleViewBookButtonClick: () => void;
  handleRefreshButtonClick: () => void;
  handleFixedColumnsButtonClick: () => void;
  handleEditPrintButtonClick: () => void;
  handleExportButtonClick: () => void;
}

const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showEditPrintDialog, setShowEditPrintDialog] = useState(false);
  const [showData, setShowData] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues | null>(null);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchFormValues) => {
    if (clearSelection) clearSelection();
    setSearchParams(values);
    setInitialSearchDialogOpen(false);
    setShowData(true);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const openEditPrintDialog = () => setShowEditPrintDialog(true);
  const closeEditPrintDialog = () => setShowEditPrintDialog(false);

  const handleViewBookButtonClick = () => {
    // TODO: Implement view book
  };

  const handleRefreshButtonClick = () => {
    // TODO: Implement refresh
  };

  const handleFixedColumnsButtonClick = () => {
    // TODO: Implement fixed columns
  };

  const handleEditPrintButtonClick = () => {
    openEditPrintDialog();
    // TODO: Implement edit print
  };

  const handleExportButtonClick = () => {
    // TODO: Implement export
  };

  return {
    initialSearchDialogOpen,
    showEditPrintDialog,
    showData,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    openEditPrintDialog,
    closeEditPrintDialog,

    handleViewBookButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleEditPrintButtonClick,
    handleExportButtonClick
  };
};

export default useDialogState;
