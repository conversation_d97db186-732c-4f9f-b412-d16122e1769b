import { useState, useCallback, useEffect } from 'react';
import {
  transformFormDataToAPI,
  transformAPIResponseToFrontend,
  createSummaryRow as createSummaryRowUtil,
  formatDateForAPI
} from '../utils/transformData';
import {
  MultiPeriodSalesItem,
  MultiPeriodSalesResponse,
  MultiPeriodSalesSearchFormValues,
  UseMultiPeriodSalesReturn
} from '@/types/schemas';
import { useBaoCaoBanHangNhieuKy } from '@/hooks/queries/useBaoCaoBanHangNhieuKy';
import api from '@/lib/api';

/**
 * Custom hook for managing Multi-Period Sales Report data
 *
 * This hook provides functionality to fetch multi-period sales data
 * with server-side pagination support for handling large datasets.
 */
export function useReportData(searchParams: MultiPeriodSalesSearchFormValues): UseMultiPeriodSalesReturn {
  const [data, setData] = useState<MultiPeriodSalesItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize] = useState<number>(20); // Default page size
  const [periods, setPeriods] = useState<string[]>([]);
  const [reportStructure, setReportStructure] = useState<any>(null);

  const { submitBaoCaoBanHangNhieuKy } = useBaoCaoBanHangNhieuKy();

  const fetchData = useCallback(
    async (searchParams: MultiPeriodSalesSearchFormValues) => {
      setIsLoading(true);
      setError(null);

      try {
        // Transform form data to API format and add pagination
        const apiParams = transformFormDataToAPI({
          ...searchParams,
          ngay_ct1: formatDateForAPI(searchParams.ngay_ct1),
          ngay_ct2: formatDateForAPI(searchParams.ngay_ct2),
          page: currentPage + 1, // API expects 1-based page numbers
          page_size: pageSize
        });

        const response = await submitBaoCaoBanHangNhieuKy(apiParams);

        // Transform API response to frontend format
        const transformedResponse = transformAPIResponseToFrontend(response);
        const rawData = transformedResponse.results || [];

        // Set periods and report structure from response
        if (transformedResponse.periods) {
          setPeriods(transformedResponse.periods);
        }
        if (transformedResponse.report_structure) {
          setReportStructure(transformedResponse.report_structure);
        }

        // Set data without summary row
        setData(rawData);

        setTotalItems(transformedResponse.count || 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [submitBaoCaoBanHangNhieuKy, currentPage, pageSize]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const postData = useCallback(
    async (searchParams: MultiPeriodSalesSearchFormValues) => {
      setIsLoading(true);
      setError(null);

      try {
        // Transform form data to API format and add pagination
        const apiParams = transformFormDataToAPI({
          ...searchParams,
          ngay_ct1: formatDateForAPI(searchParams.ngay_ct1),
          ngay_ct2: formatDateForAPI(searchParams.ngay_ct2),
          page: currentPage + 1, // API expects 1-based page numbers
          page_size: pageSize
        });

        const response = await api.post<MultiPeriodSalesResponse>(
          '/ban-hang/bao-cao-ban-hang/bao-cao-ban-hang-nhieu-ky/',
          apiParams
        );

        // Transform API response to frontend format
        const transformedResponse = transformAPIResponseToFrontend(response.data);
        const rawData = transformedResponse.results || [];

        // Set periods and report structure from response
        if (transformedResponse.periods) {
          setPeriods(transformedResponse.periods);
        }
        if (transformedResponse.report_structure) {
          setReportStructure(transformedResponse.report_structure);
        }

        // Only add summary row on first page
        if (currentPage === 0 && rawData.length > 0) {
          // Create summary row and add it to the beginning
          const summaryRow = createSummaryRowUtil(rawData, transformedResponse.periods);
          const dataWithSummary = [summaryRow, ...rawData];
          setData(dataWithSummary);
        } else {
          setData(rawData);
        }

        setTotalItems(transformedResponse.count || 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while posting data';
        setError(new Error(errorMessage));
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [currentPage, pageSize]
  );

  const handlePageChange = useCallback(async (page: number) => {
    setCurrentPage(page);
    // The useEffect will automatically trigger fetchData when currentPage changes
  }, []);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    if (Object.keys(searchParams).length > 0) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    totalItems,
    currentPage,
    pageSize,
    periods,
    reportStructure,
    fetchData,
    refreshData,
    postData,
    handlePageChange
  };
}
