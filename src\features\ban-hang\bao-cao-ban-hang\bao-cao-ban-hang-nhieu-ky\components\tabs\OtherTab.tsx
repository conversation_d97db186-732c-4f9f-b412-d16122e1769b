import React, { useState } from 'react';

import {
  accountSearchColumns,
  giaoDichSearchColumns,
  loSearchColumns,
  nhanVienSearchColumns,
  viTriSearchColumns,
  QUERY_KEYS
} from '@/constants';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField, SearchField } from '@/components/custom/arito';
import { useSearchFieldIntegration } from '../../hooks';
import { Label } from '@/components/ui/label';

import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const {
    handleTransactionCodeSelection,
    handleItemAccountSelection,
    handleEmployeeCodeSelection,
    handleRevenueAccountSelection,
    handleCostAccountSelection,
    handleBatchCodeSelection,
    handleLocationCodeSelection,
    getFormValue
  } = useSearchFieldIntegration();

  const handleSaveFilterTemplate = (_data: SaveTemplateFormData) => {
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <SearchField
            searchEndpoint='/api/transactions'
            searchColumns={giaoDichSearchColumns}
            columnDisplay='transactionCode'
            displayRelatedField='transactionName'
            value={getFormValue('transactionCode_data')?.transactionCode || ''}
            onRowSelection={handleTransactionCodeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            columnDisplay='code'
            displayRelatedField='name'
            value={getFormValue('itemAccount_data')?.code || ''}
            onRowSelection={handleItemAccountSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
            searchColumns={nhanVienSearchColumns}
            columnDisplay='ma_nhan_vien'
            displayRelatedField='ho_ten_nhan_vien'
            value={getFormValue('employeeCode_data')?.ma_nhan_vien || ''}
            onRowSelection={handleEmployeeCodeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            columnDisplay='code'
            displayRelatedField='name'
            value={getFormValue('revenueAccount_data')?.code || ''}
            onRowSelection={handleRevenueAccountSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            columnDisplay='code'
            displayRelatedField='name'
            value={getFormValue('costAccount_data')?.code || ''}
            onRowSelection={handleCostAccountSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.LO}/`}
            searchColumns={loSearchColumns}
            columnDisplay='ma_lo'
            displayRelatedField='ten_lo'
            value={getFormValue('batchCode_data')?.ma_lo || ''}
            onRowSelection={handleBatchCodeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
            searchColumns={viTriSearchColumns}
            columnDisplay='ma_vi_tri'
            displayRelatedField='ten_vi_tri'
            value={getFormValue('locationCode_data')?.ma_vi_tri || ''}
            onRowSelection={handleLocationCodeSelection}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ (từ/đến):</Label>
          <div className='flex gap-2'>
            <FormField name='so_ct1' type='text' className='w-32' />
            <FormField name='so_ct2' type='text' className='w-32' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='dien_giai' type='text' className='w-32' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-2'>
            <FormField
              name='report_filtering'
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-48'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => {}
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => {}
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
