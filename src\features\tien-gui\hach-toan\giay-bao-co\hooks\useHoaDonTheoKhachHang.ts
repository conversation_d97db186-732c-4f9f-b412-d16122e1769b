import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface HoaDonData {
  so_ct: string;
  ngay_ct: string;
  [key: string]: any;
}

interface UseHoaDonTheoKhachHangReturn {
  hoaDonData: HoaDonData[];
  isLoading: boolean;
  error: string | null;
  fetchHoaDonData: (customerCode: string) => Promise<void>;
  getNgayCtBySoCt: (soCt: string) => string;
}

export const useHoaDonTheoKhachHang = (): UseHoaDonTheoKhachHangReturn => {
  const [hoaDonData, setHoaDonData] = useState<HoaDonData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { entity } = useAuth();

  const fetchHoaDonData = useCallback(
    async (customerCode: string) => {
      if (!entity?.slug || !customerCode) {
        setHoaDonData([]);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.get(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${customerCode}&context=GBC`
        );

        const data = response.data?.results || response.data || [];
        setHoaDonData(Array.isArray(data) ? data : []);
      } catch (error) {
        console.error('Error fetching hoa don data:', error);
        setError(error instanceof Error ? error.message : 'Không thể tải dữ liệu hóa đơn');
        setHoaDonData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const getNgayCtBySoCt = useCallback(
    (soCt: string): string => {
      if (!soCt || !hoaDonData.length) return '';

      const hoaDon = hoaDonData.find(item => item.so_ct === soCt);
      return hoaDon?.ngay_ct || '';
    },
    [hoaDonData]
  );

  return {
    hoaDonData,
    isLoading,
    error,
    fetchHoaDonData,
    getNgayCtBySoCt
  };
};
