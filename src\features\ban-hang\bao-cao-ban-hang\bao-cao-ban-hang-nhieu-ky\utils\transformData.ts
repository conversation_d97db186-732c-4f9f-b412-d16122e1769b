import { MultiPeriodSalesSearchFormValues } from '@/types/schemas';

/**
 * Transform form data to API request format
 * Converts frontend form structure to backend API expected format
 */
export function transformFormDataToAPI(formData: MultiPeriodSalesSearchFormValues): MultiPeriodSalesSearchFormValues {
  // Create base API request object with correct field names
  const apiRequest: any = {
    // Required BasicInfo fields
    pivot_by: formData.pivot_by || 'M',
    ngay_ct1: formData.ngay_ct1,
    ngay_ct2: formData.ngay_ct2,
    so_ky: typeof formData.so_ky === 'string' ? parseInt(formData.so_ky) : formData.so_ky || 12,
    detail_by: formData.detail_by || '200',
    group_by: formData.group_by === '__all__' ? '' : formData.group_by || '',

    // DetailsTab - Customer fields (API field names with fallback to legacy names)
    ma_kh: formData.ma_kh || formData.customerCode || '',
    nh_kh1: formData.nh_kh1 || formData.customerGroup1 || '',
    nh_kh2: formData.nh_kh2 || formData.customerGroup2 || '',
    nh_kh3: formData.nh_kh3 || formData.customerGroup3 || '',
    rg_code: formData.rg_code || formData.region || '',

    // DetailsTab - Product fields (API field names with fallback to legacy names)
    ma_vt: formData.ma_vt || formData.productCode || '',
    ma_lvt: formData.ma_lvt || formData.productType || '',
    ton_kho_yn: formData.ton_kho_yn || formData.trackInventory || false,
    nh_vt1: formData.nh_vt1 || formData.productGroup1 || '',
    nh_vt2: formData.nh_vt2 || formData.productGroup2 || '',
    nh_vt3: formData.nh_vt3 || formData.productGroup3 || '',
    ma_kho: formData.ma_kho || formData.warehouseCode || '',
    ma_unit: formData.ma_unit || '',
    nh_ct: formData.nh_ct || '',

    // Additional structure fields
    loai_du_lieu: formData.loai_du_lieu || 1,
    mau_bc: formData.mau_bc || 20,
    data_analysis_struct:
      typeof formData.data_analysis_struct === 'string'
        ? parseInt(formData.data_analysis_struct)
        : formData.data_analysis_struct || 1,

    // FilterByObjectTab fields (API field names with fallback to legacy names)
    ma_bp: formData.ma_bp || formData.department || '',
    ma_vv: formData.ma_vv || formData.project || '',
    ma_hd: formData.ma_hd || formData.contract || '',
    ma_dtt: formData.ma_dtt || formData.paymentPhase || '',
    ma_ku: formData.ma_ku || formData.agreement || '',
    ma_phi: formData.ma_phi || formData.fee || '',
    ma_sp: formData.ma_sp || formData.product || '',
    ma_lsx: formData.ma_lsx || formData.productionOrder || '',
    ma_cp0: formData.ma_cp0 || formData.invalidExpense || '',

    // OtherTab fields (API field names with fallback to legacy names)
    ma_gd: formData.ma_gd || formData.transactionCode || '',
    tk_vt: formData.tk_vt || formData.itemAccount || '',
    ma_nvbh: formData.ma_nvbh || formData.employeeCode || '',
    tk_dt: formData.tk_dt || formData.revenueAccount || '',
    tk_gv: formData.tk_gv || formData.costAccount || '',
    ma_lo: formData.ma_lo || formData.batchCode || '',
    ma_vi_tri: formData.ma_vi_tri || formData.locationCode || '',
    so_ct1: formData.so_ct1 || formData.fromDocumentNumber || '',
    so_ct2: formData.so_ct2 || formData.toDocumentNumber || '',
    dien_giai: formData.dien_giai || formData.description || '',

    // Pagination
    page: formData.page || 1,
    page_size: formData.page_size || 20
  };

  // Keep all fields including empty strings - API expects all fields to be present
  // Convert empty strings to null for fields that should be null when not selected
  const finalRequest = { ...apiRequest };

  // Convert empty UUID fields to null
  const uuidFields = [
    'customerCode',
    'customerGroup1',
    'customerGroup2',
    'customerGroup3',
    'region',
    'productCode',
    'productGroup1',
    'productGroup2',
    'productGroup3',
    'warehouseCode',
    'department',
    'project',
    'contract',
    'paymentPhase',
    'agreement',
    'fee',
    'product',
    'productionOrder',
    'invalidExpense',
    'transactionCode',
    'itemAccount',
    'employeeCode',
    'revenueAccount',
    'costAccount',
    'batchCode',
    'locationCode'
  ];

  uuidFields.forEach(field => {
    if ((finalRequest as any)[field] === '') {
      (finalRequest as any)[field] = null;
    }
  });

  return finalRequest;
}

/**
 * Transform API response to frontend format
 * Converts backend API response to frontend expected structure
 */
export function transformAPIResponseToFrontend(apiResponse: any) {
  return {
    results: apiResponse.results || [],
    count: apiResponse.count || 0,
    next: apiResponse.next,
    previous: apiResponse.previous,
    periods: extractPeriodsFromResults(apiResponse.results || []),
    summary: apiResponse.summary || {},
    report_structure: apiResponse.report_structure || null
  };
}

/**
 * Extract period columns from API results
 * Identifies dynamic period columns like "01-2025", "02-2025", etc.
 */
function extractPeriodsFromResults(results: any[]): string[] {
  if (!results.length) return [];

  const firstItem = results[0];
  const periods: string[] = [];

  // Look for period patterns: MM-YYYY, MM/YYYY, YYYY-MM, etc.
  // Updated pattern to be more flexible and include more formats
  const periodPattern = /^(\d{1,2}[-/]\d{4}|\d{4}[-/]\d{1,2}|Q\d[-/]\d{4}|\d{4}|[A-Za-z]{3}[-/]\d{4})$/;

  Object.keys(firstItem).forEach(key => {
    // Skip standard fields that are not periods
    const standardFields = ['ma', 'ten', 'id', 'uuid', 'tong_cong', 'systotal'];
    if (!standardFields.includes(key) && periodPattern.test(key)) {
      periods.push(key);
    }
  });

  // Sort periods chronologically
  return periods.sort((a, b) => {
    // Simple string sort should work for most period formats
    return a.localeCompare(b);
  });
}

/**
 * Format date for API request
 * Converts various date formats to API expected format
 */
export function formatDateForAPI(date: string): string {
  if (!date) return '';

  // If already in YYYYMMDD format, return as is
  if (/^\d{8}$/.test(date)) {
    return date;
  }

  // If in YYYY-MM-DD format, convert to YYYYMMDD
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return date.replace(/-/g, '');
  }

  // Try to parse as Date and format
  try {
    const dateObj = new Date(date);
    if (!isNaN(dateObj.getTime())) {
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      return `${year}${month}${day}`;
    }
  } catch (error) {
    console.warn('Failed to parse date:', date);
  }

  return date;
}

/**
 * Create summary row from data
 * Generates a summary row for display at the top of the table
 */
export function createSummaryRow(data: any[], periods: string[]): any {
  if (!data.length) return null;

  const summary: any = {
    id: 'SUMMARY_ROW',
    ma: '',
    ten: 'TỔNG CỘNG',
    systotal: 'TOTAL'
  };

  // Sum up period values
  periods.forEach(period => {
    summary[period] = data.reduce((sum, item) => {
      const value = parseFloat(item[period]?.toString() || '0') || 0;
      return sum + value;
    }, 0);
  });

  // Sum up total if exists
  if (data.some(item => Object.prototype.hasOwnProperty.call(item, 'tong_cong'))) {
    summary.tong_cong = data.reduce((sum, item) => {
      const value = parseFloat(item.tong_cong?.toString() || '0') || 0;
      return sum + value;
    }, 0);
  }

  return summary;
}
