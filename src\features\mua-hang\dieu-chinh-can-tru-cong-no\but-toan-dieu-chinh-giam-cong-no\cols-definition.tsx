import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { ngoaiTeSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';
import { QUERY_KEYS } from '@/constants/query-keys';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 't_ps_no_nt',
    headerName: 'Tổng phát sinh',
    width: 150
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
        searchColumns={ngoaiTeSearchColumns}
        dialogTitle='Danh mục ngoại tệ'
        columnDisplay='ma_nt'
        value={params.row.ma_nt_data?.ma_nt || params.row.ma_nt || ''}
        disabled
      />
    )
  },
  {
    field: 'username0',
    headerName: 'Người tạo',
    width: 120
  },
  {
    field: 'datetime0',
    headerName: 'Ngày tạo',
    width: 120,
    renderCell: params => (params.row.datetime0 ? format(new Date(params.row.datetime0), 'dd/MM/yyyy HH:mm') : '')
  },
  {
    field: 'username2',
    headerName: 'Người sửa',
    width: 120
  },
  {
    field: 'datetime2',
    headerName: 'Ngày sửa',
    width: 120,
    renderCell: params => (params.row.datetime2 ? format(new Date(params.row.datetime2), 'dd/MM/yyyy HH:mm') : '')
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200
  },
  {
    field: 'id_hd',
    headerName: 'Hóa đơn',
    width: 120
  },
  {
    field: 'ngay_ct_hd',
    headerName: 'Ngày hóa đơn',
    width: 120
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 150
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 200
  },
  {
    field: 'ma_nt_hd',
    headerName: 'Ngoại tệ',
    width: 120
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 120
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vu_viec
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'lenh_sx',
    headerName: 'Lệnh sản xuất',
    width: 150
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => params.row.ma_cp0_data?.ma_cp
  }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
