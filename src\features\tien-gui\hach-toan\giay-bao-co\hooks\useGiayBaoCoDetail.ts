import { useState, useEffect, useCallback } from 'react';
import { useGiayBaoCo } from '@/hooks';

interface UseGiayBaoCoDetailReturn {
  detail: any[];
  isLoadingDetail: boolean;
  error: string | null;
  fetchDetail: () => Promise<void>;
  clearDetail: () => void;
}

export const useGiayBaoCoDetail = (selectedUuid?: string): UseGiayBaoCoDetailReturn => {
  const [detail, setDetail] = useState<any[]>([]);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getGiayBaoCoDetail } = useGiayBaoCo();

  const fetchDetail = useCallback(async () => {
    setDetail([]);
    setError(null);

    if (!selectedUuid) {
      setDetail([]);
      return;
    }

    console.log('Fetching detail for UUID:', selectedUuid);
    setIsLoadingDetail(true);

    try {
      const detailData = await getGiayBaoCoDetail(selectedUuid);
      console.log('Raw detail data:', detailData);
      console.log('Data type:', typeof detailData);
      console.log('Is array:', Array.isArray(detailData));

      // Ensure we always set an array
      if (Array.isArray(detailData)) {
        setDetail(detailData);
      } else if (detailData && typeof detailData === 'object') {
        // Check if it's a paginated response with results
        if ((detailData as any).results && Array.isArray((detailData as any).results)) {
          setDetail((detailData as any).results);
        } else {
          console.warn('Unexpected data structure, setting empty array');
          setDetail([]);
        }
      } else {
        setDetail([]);
      }
    } catch (error) {
      console.error('Error fetching detail:', error);
      const errorMessage = error instanceof Error ? error.message : 'Không thể tải chi tiết giấy báo có';
      setError(errorMessage);
      setDetail([]);
    } finally {
      setIsLoadingDetail(false);
    }
  }, [selectedUuid, getGiayBaoCoDetail]);

  const clearDetail = useCallback(() => {
    setDetail([]);
    setError(null);
  }, []);

  useEffect(() => {
    if (selectedUuid) {
      fetchDetail();
    } else {
      setDetail([]);
      setError(null);
    }
  }, [selectedUuid]);

  return {
    detail,
    isLoadingDetail,
    error,
    fetchDetail,
    clearDetail
  };
};
