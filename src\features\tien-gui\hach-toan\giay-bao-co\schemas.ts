import { z } from 'zod';

export const exportCreditAdviceSchema = z.object({
  ma_ngv: z.string().default('1'),
  dia_chi: z.string().optional(),
  ong_ba: z.string().optional(),
  dien_giai: z.string().optional(),
  ngay_ct: z.string().optional(),
  ngay_lct: z.string().optional(),
  ngay_ct0: z.string().optional(),
  ma_nt: z.string().optional(),
  ty_gia: z.string().optional(),

  status: z.string().optional(),
  transfer_yn: z.boolean().optional()
});

export type ExportCreditAdviceFormValues = z.infer<typeof exportCreditAdviceSchema>;

export const initialFormValues: ExportCreditAdviceFormValues = {
  ma_ngv: '1',
  dia_chi: '',
  ong_ba: '',
  dien_giai: '',
  ngay_ct: new Date().toISOString().split('T')[0],
  ngay_lct: new Date().toISOString().split('T')[0],
  ngay_ct0: new Date().toISOString().split('T')[0],
  ma_nt: 'VND',
  ty_gia: '1',
  status: '0',
  transfer_yn: false
};

export const searchSchema = z.object({
  ma_ngv: z.string().optional(),
  from_date: z.string().optional(),
  to_date: z.string().optional(),
  from_number: z.string().optional(),
  to_number: z.string().optional(),
  loai_xuat: z.string().default('Tất cả'),
  tai_khoan_no: z.string().optional(),
  dien_giai: z.string().optional(),
  ma_khach_hang: z.string().optional(),
  tai_khoan_co: z.string().optional(),
  don_vi: z.string().optional(),
  trang_thai: z.string().default('Tất cả'),
  loc_theo_nguoi_sd: z.string().default('Tất cả')
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  ma_ngv: '1',
  from_date: '2025-05-01',
  to_date: '2025-05-31',
  from_number: '',
  to_number: '',
  loai_xuat: 'Tất cả',
  tai_khoan_no: '',
  dien_giai: '',
  ma_khach_hang: '',
  tai_khoan_co: '',
  don_vi: '',
  trang_thai: 'Tất cả',
  loc_theo_nguoi_sd: 'Tất cả'
};
