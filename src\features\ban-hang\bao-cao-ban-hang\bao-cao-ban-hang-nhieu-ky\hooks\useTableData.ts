import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { multiPeriodSalesColumns, fallbackColumns } from '../cols-definition';
import { MultiPeriodSalesSearchFormValues } from '@/types/schemas';
import { useReportData } from './useReportData';

export interface UseTableDataReturn {
  tables: Array<{
    name: string;
    rows: any[];
    columns: any[];
  }>;
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  totalItems: number;
  currentPage: number;
  handlePageChange: (page: number) => Promise<void>;
}

export function useTableData(searchParams: MultiPeriodSalesSearchFormValues): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const {
    data,
    isLoading,
    error,
    totalItems,
    currentPage,
    pageSize,
    periods,
    reportStructure,
    refreshData,
    handlePageChange
  } = useReportData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const row = params.row as any;
    setSelectedRowIndex(params.row.id?.toString());
  };

  // Generate columns based on periods and data
  const columns = periods.length > 0 ? multiPeriodSalesColumns(periods, data) : fallbackColumns;

  const tables = [
    {
      name: 'Báo cáo bán hàng nhiều kỳ',
      rows: data,
      columns: columns
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData,
    totalItems,
    currentPage,
    handlePageChange
  };
}
