import React, { useState } from 'react';

import { AritoDialog, AritoIcon, AritoForm, BottomBar, AritoHeaderTabs } from '@/components/custom/arito';
import { transformFormDataToAPI, formatDateForAPI } from '../utils/transformData';
import { DetailsTab, FilterByObjectTab, OtherTab, BasicInfo } from './tabs';
import { searchSchema, initialValues } from '../schema';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);
  const { resetSearchFields, getSearchFieldData } = useSearchFieldStates();

  const handleSubmit = (data: any) => {
    // Get search field data (UUID values)
    const searchFieldData = getSearchFieldData();

    // Combine form data with search field data
    // Search field data first, then form data overrides
    const combinedData = {
      ...searchFieldData, // API field names with UUIDs
      ...data // Form data
    };

    // Transform to API format with type conversions
    const apiData = transformFormDataToAPI({
      ...combinedData,
      ngay_ct1: formatDateForAPI(combinedData.ngay_ct1),
      ngay_ct2: formatDateForAPI(combinedData.ngay_ct2),
      so_ky: typeof combinedData.so_ky === 'string' ? parseInt(combinedData.so_ky) : combinedData.so_ky
    });

    onSearch(apiData);
    onClose();
  };

  const handleDirectSubmit = () => {
    // Get search field data (UUID values)
    const searchFieldData = getSearchFieldData();

    // Combine form values with search field data
    const combinedData = {
      ...searchFieldData, // API field names with UUIDs
      ...formValues // Form values
    };

    // Transform to API format with type conversions
    const apiData = transformFormDataToAPI({
      ...combinedData,
      ngay_ct1: formatDateForAPI(combinedData.ngay_ct1),
      ngay_ct2: formatDateForAPI(combinedData.ngay_ct2),
      so_ky: typeof combinedData.so_ky === 'string' ? parseInt(combinedData.so_ky) : combinedData.so_ky
    });

    onSearch(apiData);
    onClose();
  };

  const handleClose = () => {
    resetSearchFields();
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title='Báo cáo bán hàng nhiều kỳ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'filter-by-object',
                  label: 'Lọc theo đối tượng',
                  component: <FilterByObjectTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='add' onSubmit={handleDirectSubmit} onClose={handleClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
