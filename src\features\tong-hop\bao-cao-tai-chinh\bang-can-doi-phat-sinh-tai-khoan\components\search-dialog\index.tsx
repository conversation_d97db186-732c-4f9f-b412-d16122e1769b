import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, searchFormSchema, initialSearchValues } from '../../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { DetailTab } from './DetailTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: SearchFormValues) => void;
}

const SearchDialog = ({ openSearchDialog, onCloseSearchDialog, onSearch }: SearchDialogProps) => {
  const searchFieldStates = useSearchFieldStates();

  const handleSubmit = (data: SearchFormValues) => {
    // Get SearchField data
    const searchFieldData = searchFieldStates.getSearchFieldData();

    // Combine schema data with SearchField data
    const combinedData = {
      ...data,
      ...searchFieldData
    };

    onSearch(combinedData);
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Bảng cân đối phát sinh tài khoản'
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail_tab',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' searchFieldStates={searchFieldStates} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
            <Button onClick={onCloseSearchDialog} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
