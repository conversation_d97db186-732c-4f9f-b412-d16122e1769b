import { GridCellParams, GridEventListener } from '@mui/x-data-grid';
import { useState } from 'react';
import type { FormFieldState } from '../../../hooks';

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useDetailRows<
  T extends {
    uuid: string;
    line?: number;
  }
>(
  initialRows: T[] = [],
  onChange?: (rows: T[]) => void,
  onFieldValueChange?: (row: T, field: string, value: any) => Partial<T>,
) {
  const [rows, setRows] = useState<T[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<T | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleRowClick: GridEventListener<'rowClick'> = params => {
    const rowUuid = params.id?.toString() || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as T);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);

    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: T[keyof T]) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const currentRow = rows[rowIndex];
    const extraFields = onFieldValueChange?.(currentRow, field, newValue) ?? {};

    const updatedRow = {
      ...currentRow,
      [field]: newValue,
      ...extraFields
    };

    const updatedRows = [...rows];
    updatedRows[rowIndex] = updatedRow;

    setRows(updatedRows);
    onChange?.(updatedRows);

    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRow);
    }
  };

  const handleAddRow = () => {
    const newRow = {
      uuid: `temp-${Date.now()}`,
      ma_loai_hd: '0',
      ...(rows.length > 0 && rows[0].line !== undefined && { line: rows.length + 1 })
    } as unknown as T;

    const newRows = [...rows, newRow];
    setRows(newRows);
    onChange?.(newRows);

    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    if (!selectedRowUuid) return;

    const updatedRows = rows.filter((row: T) => row.uuid !== selectedRowUuid);

    const reorderedRows = updatedRows.map((row: T, index: number) => ({
      ...row,
      ...(row.line !== undefined && { line: index + 1 })
    }));

    setRows(reorderedRows);
    onChange?.(reorderedRows);

    // After deletion, select the last row if there are remaining rows
    if (reorderedRows.length > 0) {
      const lastRow = reorderedRows[reorderedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid);
      setSelectedRow(lastRow);
    } else {
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const copiedRow: T = {
      ...rowDataWithoutUuid,
      uuid: `temp-${Date.now()}`,
      ...(selectedRow.line !== undefined && { line: rows.length + 1 })
    } as T;

    const newRows = [...rows, copiedRow];
    setRows(newRows);

    setSelectedRowUuid(copiedRow.uuid);
    setSelectedRow(copiedRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow: T = {
      ...rowDataWithoutUuid,
      uuid: `temp-${Date.now()}`,
      ...(selectedRow.line !== undefined && { line: rows.length + 1 })
    } as T;

    let newRows: T[] = [];

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);
    } else {
      newRows = [...rows, newRow];
    }

    setRows(newRows);

    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex((row: T) => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    newRows.forEach((row: T, index: number) => {
      if (row.line !== undefined) {
        (row as any).line = index + 1;
      }
    });

    setRows(newRows);

    setSelectedRow(movedRow);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleCellValueChange,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow
  };
}
