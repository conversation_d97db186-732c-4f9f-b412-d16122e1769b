import { z } from 'zod';

export const searchFormSchema = z.object({
  ngay_ct1: z.string().optional(),
  ngay_ct2: z.string().optional(),
  ngay_ms: z.string().optional(),
  tk_ps_yn: z.boolean().optional(),
  bac_tk: z.coerce.number().optional(),
  loai_tk: z.number().optional(),
  bu_tru: z.number().optional(),
  loai_nhom: z.number().optional(),
  nguoi_lap: z.string().optional(),
  ma_unit: z.string().optional(),
  mau_bc: z.number().optional()
});
export type SearchFormValues = z.infer<typeof searchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date().toISOString().split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  ngay_ms: new Date().toISOString().split('T')[0],
  tk_ps_yn: false,
  bac_tk: 0,
  loai_tk: 0,
  bu_tru: 0,
  loai_nhom: 0,
  nguoi_lap: '',
  ma_unit: '',
  mau_bc: 20
};

// Trial Balance Data Types
export interface TrialBalanceItem {
  id: string;
  sysorder?: number;
  systotal?: boolean;
  sysprint?: boolean;
  syspivot?: boolean;
  stt?: number | string;
  tk: string; // tai_khoan
  no_dk: number | string; // du_no_dau
  co_dk: number | string; // du_co_dau
  ps_no: number | string; // ps_no
  ps_co: number | string; // ps_co
  no_ck: number | string; // du_no_cuoi
  co_ck: number | string; // du_co_cuoi
  bac_tk?: number;
  ten_tk: string; // ten_tai_khoan
  xten_tk?: string;
  xten_tk2?: string;
  ct_yn?: boolean;
  bold_yn?: boolean;
  isSummary?: boolean;
}

export interface TrialBalanceResponse {
  results: TrialBalanceItem[];
  count: number;
}

export interface UseTrialBalanceReturn {
  data: TrialBalanceItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
