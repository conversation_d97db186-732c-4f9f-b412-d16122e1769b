export const calculateTotals = (details: any[]) => {
  return details.reduce((acc, detail) => acc + (Number(detail.tien_nt) || 0), 0);
};

export const calculatePaymentTotals = (detailRows: any[], bankFeeRows: any[]) => {
  const tong_tien = calculateTotals(detailRows);

  const total_tien_cp_nt = bankFeeRows.reduce((acc, row) => acc + (Number(row.tien_cp_nt) || 0), 0);
  const total_t_thue_nt = bankFeeRows.reduce((acc, row) => acc + (Number(row.t_thue_nt) || 0), 0);
  const tong_thue = total_tien_cp_nt + total_t_thue_nt;

  const tong_thanh_toan = tong_tien - total_tien_cp_nt - total_t_thue_nt;

  return {
    tong_tien,
    tong_thanh_toan,
    tong_thue
  };
};
