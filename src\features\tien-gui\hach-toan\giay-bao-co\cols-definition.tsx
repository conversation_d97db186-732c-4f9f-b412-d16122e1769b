import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { formatDate } from '@/lib/formatUtils';

export const getChangingValueCreditAdviceColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 100,
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 100 },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'sten_tknh', headerName: 'Ngân hàng', width: 120, renderCell: params => params.row.tknh_data?.name },
  { field: 'tk', headerName: 'Tài khoản nợ  ', width: 100, renderCell: params => params.row.tk_data?.code },
  { field: 't_tien_nt', headerName: 'Tổng tiền', width: 120 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 100, renderCell: params => params.row.ma_nt_data?.ma_nt },
  {
    field: 'ma_ngv',
    headerName: 'Loại chứng từ',
    width: 120,
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '1':
          return '1. Theo hoá đơn';
        case '2':
          return '2. Theo đối tượng';
        case '3':
          return '3. Thu khác';
      }
    }
  }
];

export const exportCreditAdviceDetailColumns: GridColDef[] = [
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code || params.row.ma_kh || ''
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name || ''
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.credit_limit || ''
  },
  { field: 'id_hd', headerName: 'Hoá đơn', width: 120, renderCell: params => params.row.id_hd_data?.so_ct || '' },
  {
    field: 'ngay_ct_hd',
    headerName: 'Ngày hoá đơn',
    width: 120,
    renderCell: params => formatDate(params.row.id_hd_data?.ngay_ct) || ''
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 120,
    renderCell: params => params.row.tk_co_data?.code || params.row.tk_co || ''
  },
  {
    field: 'ma_nt_hd',
    headerName: 'Ngoại tệ',
    width: 200,
    renderCell: params => params.row.id_hd_data?.ngoai_te || ''
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 120,
    renderCell: params => params.row.id_hd_data?.ty_gia || 1
  },
  {
    field: 'tien_hd_nt',
    headerName: 'Tiền trên hoá đơn',
    type: 'number',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_tren_hd
  },
  { field: 'da_pb_nt', headerName: 'Đã phân bổ', type: 'number', width: 120 },
  {
    field: 'cl_nt',
    headerName: 'Còn lại',
    type: 'number',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_con_phai_tt
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    type: 'number',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_hd_nt
  },
  { field: 'ma_vv', headerName: 'Vụ việc', width: 200, renderCell: params => params.row.ma_vv_data?.ma_vu_viec },
  { field: 'ma_hd', headerName: 'Hợp đồng', width: 200, renderCell: params => params.row.ma_hd_data?.ma_hd },
  { field: 'ma_dtt', headerName: 'Đợt thanh toán', width: 200, renderCell: params => params.row.ma_dtt_data?.ma_dtt },
  { field: 'ma_ku', headerName: 'Khế ước', width: 200, renderCell: params => params.row.ma_ku_data?.ma_ku },
  { field: 'ma_phi', headerName: 'Phí', width: 200, renderCell: params => params.row.ma_phi_data?.ma_phi },
  { field: 'ma_sp', headerName: 'Sản phẩm', width: 200, renderCell: params => params.row.ma_sp_data?.ma_vt },
  { field: 'ma_lsx', headerName: 'Lệnh sản xuất', width: 200 },
  { field: 'ma_cp0', headerName: 'C/p không h/lệ', width: 200, renderCell: params => params.row.ma_cp0_data?.ma_cpkhl }
];
