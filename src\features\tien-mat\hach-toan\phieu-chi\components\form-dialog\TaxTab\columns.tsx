import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  dotThanhToanSearchColumns,
  hanThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  mauSoHoaDonSearchColumns,
  phiSearchColumns,
  thueSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  KhachHang,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  MauSoHoaDon,
  HanThanhToan,
  type ChiPhiKhongHopLeData
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getTaxTableColumns = (
  taxRates: any[],
  mauSoHoaDons: any[],
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 120,
    renderCell: params => (
      <CellField
        name='so_ct0'
        value={params.row.so_ct0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 120,
    renderCell: params => (
      <CellField
        name='so_ct2'
        value={params.row.so_ct2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hoá đơn',
    width: 120,
    renderCell: params => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },

  {
    field: 'ma_thue',
    headerName: 'Mã thuế',
    width: 120,
    renderCell: (params: any) => (
      <SearchField
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        columnDisplay='ma_thue'
        dialogTitle='Danh mục thuế'
        value={params.row.ma_thue_data?.ma_thue || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'ma_mau_ct',
    headerName: 'Mẫu hoá đơn',
    width: 120,
    renderCell: params => {
      return (
        <SearchField<MauSoHoaDon>
          searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
          searchColumns={mauSoHoaDonSearchColumns}
          columnDisplay='ma_mau_so'
          dialogTitle='Danh mục mẫu chứng từ'
          value={params.row.ma_mau_ct_data?.ma_mau_so}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
        />
      );
    }
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mã mẫu BC',
    width: 120,
    renderCell: params => {
      const mauSoHoaDonOptions = mauSoHoaDons.map(item => ({
        value: item.uuid,
        label: `${item.ma_mau_so} - ${item.ten_mau_so}`
      }));

      return (
        <CellField
          name='ma_mau_bc'
          type='select'
          value={params.row.ma_mau_bc}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
          options={mauSoHoaDonOptions}
        />
      );
    }
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 120,
    renderCell: params => (
      <CellField
        name='ma_tc_thue'
        type='select'
        value={params.row.ma_tc_thue}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_tc_thue', newValue)}
        options={[]}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã ncc',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        dialogTitle='Danh mục khách hàng'
        columnDisplay='customer_code'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 200,
    renderCell: params => (
      <CellField
        name='ten_kh_thue'
        value={params.row.ma_kh_data?.customer_name || params.row.ten_kh_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
      />
    )
  },
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 200,
    renderCell: params => (
      <CellField
        name='dia_chi'
        value={params.row.ma_kh_data?.address || params.row.dia_chi}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
      />
    )
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    renderCell: params => (
      <CellField
        name='ma_so_thue'
        value={params.row.ma_kh_data?.tax_code || params.row.ma_so_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
      />
    )
  },
  {
    field: 'ten_vt_thue',
    headerName: 'Tên hàng hoá - dịch vụ',
    width: 200,
    renderCell: params => (
      <CellField
        name='ten_vt_thue'
        value={params.row.ten_vt_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
      />
    )
  },
  {
    field: 't_tien_nt',
    headerName: 'Tiền hàng VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='t_tien_nt'
        type='number'
        value={params.row.t_tien_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_tien_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_thue_no',
    headerName: 'TK thuế',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_thue_no_data?.code || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'tk_thue_no_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'TK đối ứng',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_kh9',
    headerName: 'Cục thuế',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        dialogTitle='Danh mục đối tượng'
        columnDisplay='customer_code'
        value={params.row.ma_kh9_data?.customer_code || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
      />
    )
  },
  {
    field: 'ma_tt',
    headerName: 'Mã thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<HanThanhToan>
        searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        searchColumns={hanThanhToanSearchColumns}
        dialogTitle='Danh mục thanh toán'
        columnDisplay='ma_tt'
        value={params.row.ma_tt_data?.ma_tt || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_tt_data', row)}
      />
    )
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: params => (
      <CellField
        name='ghi_chu'
        value={params.row.ghi_chu}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        dialogTitle='Danh mục bộ phận'
        columnDisplay='ma_bp'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        dialogTitle='Danh mục vụ việc'
        columnDisplay='ma_vu_viec'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Mã hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        dialogTitle='Danh mục hợp đồng'
        columnDisplay='ma_hd'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        dialogTitle='Danh mục đợt thanh toán'
        columnDisplay='ma_dtt'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        dialogTitle='Danh mục khế ước'
        columnDisplay='ma_ku'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        dialogTitle='Danh mục phí'
        columnDisplay='ma_phi'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        dialogTitle='Danh mục sản phẩm'
        columnDisplay='ma_vt'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        dialogTitle='Danh mục lệnh sản xuất'
        columnDisplay='ma_lsx'
        value={params.row.ma_lsx || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        dialogTitle='Danh mục chi phí không hợp lệ'
        columnDisplay='ma_cp_khl'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        onRowSelection={row => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
