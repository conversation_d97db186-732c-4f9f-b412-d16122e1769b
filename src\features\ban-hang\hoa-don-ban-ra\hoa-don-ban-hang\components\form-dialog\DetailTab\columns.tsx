import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  thueSearchColumns,
  vatTu1SearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  warehouseSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  ChiPhiKhongHopLeData,
  DieuChinhBoPhanSuDungCCDCInput,
  DotThanhToan,
  HopDong,
  KheUoc,
  KhoHang,
  Phi,
  Tax,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';

export const getDetailTableColumns = (
  taxRates: any[],
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTu1SearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục vật tư'
        value={params.row.ma_vt_data?.ma_vt || ''}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_vt_data', row);
          console.log('Row selected:', params.row);
        }}
      />
    )
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => <CellField name='ten_vt' type='text' value={params.row.ma_vt_data?.ten_vt || ''} />
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => (
      <CellField
        name='dvt'
        type='select'
        value={params.row.ma_vt_data?.dvt}
        options={[{ value: `${params.row.ma_vt_data?.dvt}`, label: `${params.row.ma_vt_data?.dvt_data?.dvt}` }]}
      />
    )
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: params => (
      <SearchField<KhoHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
        searchColumns={warehouseSearchColumns}
        columnDisplay='ma_kho'
        dialogTitle='Danh mục kho hàng'
        value={params.row.ma_kho_data?.ma_kho}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kho_data', row)}
      />
    )
  },
  {
    field: 'sl_ton',
    headerName: 'Tồn',
    width: 100,
    renderCell: params => (
      <CellField
        name='sl_ton'
        type='number'
        value={params.row.ma_vt_data?.sl_min || 0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'sl_ton', newValue)}
      />
    )
  },
  {
    field: 'ct_km',
    headerName: 'Loại hàng',
    width: 150,
    renderCell: params => (
      <CellField
        name='ct_km'
        type='select'
        options={[
          { value: '0', label: '0. Hàng bán' },
          { value: '1', label: '1. Hàng KM' }
        ]}
        value={params.row.ct_km === true ? '1' : params.row.ct_km || '0'}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ct_km', newValue)}
      />
    )
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: params => (
      <CellField
        name='so_luong'
        type='number'
        value={params.row.so_luong}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  {
    field: 'gia_nt1',
    headerName: 'Giá chuẩn VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt1'
        type='number'
        value={params.row.ma_vt_data?.gia_ton}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt2'
        type='number'
        value={params.row.gia_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt2', newValue)}
      />
    )
  },
  {
    field: 'tien_nt2',
    headerName: 'Thành tiền VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt2'
        type='number'
        value={params.row.tien_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt2', newValue)}
      />
    )
  },
  {
    field: 'px_dd',
    headerName: 'Đích danh',
    width: 120,
    type: 'boolean',
    renderCell: params => (
      <Checkbox
        checked={params.row.px_dd}
        onCheckedChange={newValue => onCellValueChange(params.row.uuid, 'px_dd', newValue)}
      />
    )
  },
  {
    field: 'gia_nt',
    headerName: 'Giá tồn VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt'
        type='number'
        value={params.row.gia_nt || (params.row.px_dd ? params.row.ma_vt_data?.gia_ton : undefined) || 0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt', newValue)}
        disabled={!params.row.px_dd}
      />
    )
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={
          params.row.tien_nt ||
          (params.row.px_dd && params.row.ma_vt_data?.gia_ton
            ? params.row.ma_vt_data.gia_ton * (params.row.so_luong || 1)
            : undefined) ||
          0
        }
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        disabled={!params.row.px_dd}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 250,
    renderCell: params => (
      <SearchField<Tax>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        columnDisplay='ma_thue'
        dialogTitle='Danh mục thuế'
        value={params.row.ma_thue_data?.ma_thue}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'tk_thue_co',
    headerName: 'Tk thuế có',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_thue_co_data?.code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_co_data', row)}
      />
    )
  },
  {
    field: 'thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='thue_nt'
        type='number'
        value={params.row.thue_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_dt',
    headerName: 'Tk doanh thu',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_vt_data?.tk_dt_data?.code || params.row.tk_dt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_dt_data', row)}
      />
    )
  },
  {
    field: 'tk_gv',
    headerName: 'Tk giá vốn',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_vt_data?.tk_gv_data?.code || params.row.tk_gv_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_gv_data', row)}
      />
    )
  },
  {
    field: 'tk_vt',
    headerName: 'Tk kho',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_vt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_vt_data', row)}
      />
    )
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_vt_data?.tk_ck_data?.code || params.row.tk_ck_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_ck_data', row)}
      />
    )
  },
  {
    field: 'tk_km',
    headerName: 'Tk khuyến mãi',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_vt_data?.tk_km_data?.code || params.row.tk_km_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_km_data', row)}
      />
    )
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú/Tên hàng hoá HĐĐT',
    width: 120,
    renderCell: params => (
      <CellField
        name='ghi_chu'
        type='text'
        value={params.row.ghi_chu || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  },
  {
    field: 'sl_px',
    headerName: 'Sl đã xuất',
    width: 120,
    renderCell: params => <CellField name='sl_px' type='text' value={params.row.sl_px} />
  },
  {
    field: 'so_ct_dh',
    headerName: 'Số đơn hàng',
    width: 120,
    renderCell: params => <CellField name='so_ct_dh' type='text' value={params.row.so_ct_dh} />
  },
  {
    field: 'line_dh',
    headerName: 'Dòng ĐH',
    width: 120,
    renderCell: params => <CellField name='line_dh' type='text' value={params.row.line_dh} />
  },
  {
    field: 'so_ct_hd',
    headerName: 'Số hoá đơn',
    width: 120,
    renderCell: params => <CellField name='so_ct_hd' type='text' value={params.row.so_ct_hd} />
  },
  {
    field: 'line_hd',
    headerName: 'Dòng HĐ',
    width: 120,
    renderCell: params => <CellField name='line_hd' type='text' value={params.row.line_hd} />
  }
];
