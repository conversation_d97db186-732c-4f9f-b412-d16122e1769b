import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useWatch } from 'react-hook-form';

interface TabProps {
  formMode: FormMode;
}

export function TyGiaTab({ formMode }: TabProps) {
  const [ma_ngv] = useWatch({ name: ['ma_ngv'] });
  return (
    <div className='p-4 space-y-5'>
      <div className='flex items-center'>
        <Label className='w-44 font-medium' htmlFor='tg_dd'>
          Sửa tỷ giá ghi sổ
        </Label>
        <FormField type='checkbox' name='tg_dd' id='tg_dd' disabled={formMode === 'view' || ma_ngv === '2'} />
      </div>
      {
        ma_ngv === '2' && (
          <div className='flex items-center font-medium'>
            <Label className='w-44' htmlFor='ty_gia'>
              <PERSON><PERSON><PERSON> chê<PERSON> lệch tỷ giá
            </Label>
            <FormField type='checkbox' name='cltg_yn' id='ty_gia' disabled={formMode === 'view'} />
          </div>
        )
      }
    </div>
  );
}
