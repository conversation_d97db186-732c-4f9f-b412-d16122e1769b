import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { PhieuThuInput } from '@/types/schemas';
import { MA_CHUNG_TU } from '@/constants';

/**
 * Transform detail rows for PhieuThu API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  if (!detailRows || detailRows.length === 0) return [];

  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    dien_giai: row.dien_giai || '',
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    id_hd: row.id_hd_data?.ID || row.id_hd || '',
    tk_co: row.id_hd_data?.tk_data?.uuid || row.tk_co || '',
    ty_gia_hd: row.ty_gia_hd?.toString() || '0',
    ty_gia2: row.ty_gia2?.toString() || '0',
    tien_nt: row.tien_nt?.toString() || '0',
    tien: row.tien?.toString() || '0',
    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || ''
  }));
};

/**
 * Transform base form data for PhieuThu submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @returns Transformed base data ready for API submission
 */
export const transformBaseData = (data: any, state: any): Partial<PhieuThuInput> => {
  return {
    ma_ngv: data.ma_ngv || '',
    dia_chi: data.dia_chi || '',
    ong_ba: data.ong_ba || '',
    dien_giai: data.dien_giai || '',

    tk: state.account?.uuid || data.tk || '',

    ...(() => {
      const docNumber = transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.TIEN_MAT.PHIEU_THU);
      return {
        ...docNumber,
        i_so_ct: docNumber.i_so_ct?.toString() || ''
      };
    })(),

    ma_kh: state.khachHang?.uuid || '',

    ngay_ct: data.ngay_ct ? format(new Date(data.ngay_ct), 'yyyy-MM-dd') : '',
    ngay_lct: data.ngay_lct ? format(new Date(data.ngay_lct), 'yyyy-MM-dd') : '',
    ngay_ct0: data.ngay_ct0 ? format(new Date(data.ngay_ct0), 'yyyy-MM-dd') : '',

    ma_nt: data.ma_nt || 'VND',
    ty_gia: data.ty_gia?.toString() || '1',

    status: data.status || '0',
    so_ct0: data.so_ct0 || '',
    so_ct_goc: data.so_ct_goc?.toString() || '0',
    dien_giai_ct_goc: data.dien_giai_ct_goc || '',

    ma_tt: state.paymentTerm?.uuid || ''
  };
};

/**
 * Transform complete form data including detail rows for full submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param calculatedData - Calculated totals from the form
 * @param entityUnit - Entity unit information
 * @returns Complete transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: any,
  detailRows: any[] = [],
  calculatedData: any,
  entityUnit: any
): any => {
  const baseData = transformBaseData(data, state);
  const child_items = transformDetailRows(detailRows);

  return {
    ...baseData,
    unit_id: entityUnit?.uuid || '',

    t_tien_nt: calculatedData.t_tien_nt?.toString() || '0',
    t_tien: calculatedData.t_tien?.toString() || '0',

    child_items
  };
};
