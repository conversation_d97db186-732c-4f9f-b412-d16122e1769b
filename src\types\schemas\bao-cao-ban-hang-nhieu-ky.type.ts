/**
 * Types for Multi-Period Sales Report (B<PERSON>o cáo bán hàng nhiều kỳ)
 */

// Search form values interface
export interface MultiPeriodSalesSearchFormValues {
  // Required BasicInfo fields
  pivot_by?: string; // Phân tích theo (D, W, M, Q, 6M, Y)
  ngay_ct1: string; // Ng<PERSON>y bắt đầu
  ngay_ct2: string; // Ngày kết thúc
  so_ky?: number; // Số kỳ phân tích
  detail_by?: string; // Báo cáo theo (1-17)
  group_by?: string; // Nhóm theo

  // DetailsTab - Customer fields (API field names)
  ma_kh?: string | null; // Mã khách hàng (UUID)
  nh_kh1?: string | null; // Nhóm khách hàng 1 (UUID)
  nh_kh2?: string | null; // Nhóm khách hàng 2 (UUID)
  nh_kh3?: string | null; // Nhóm khách hàng 3 (UUID)
  rg_code?: string | null; // Mã vùng (UUID)

  // DetailsTab - Product fields (API field names)
  ma_vt?: string | null; // Mã vật tư (UUID)
  ma_lvt?: string | null; // Loại vật tư (UUID)
  ton_kho_yn?: boolean; // Theo dõi tồn kho
  nh_vt1?: string | null; // Nhóm vật tư 1 (UUID)
  nh_vt2?: string | null; // Nhóm vật tư 2 (UUID)
  nh_vt3?: string | null; // Nhóm vật tư 3 (UUID)
  ma_kho?: string | null; // Mã kho (UUID)
  ma_unit?: string | null; // Mã đơn vị (UUID)
  nh_ct?: string | null; // Nhóm chứng từ (UUID)

  // Additional structure fields
  loai_du_lieu?: number; // Loại dữ liệu
  mau_bc?: number; // Mẫu báo cáo
  data_analysis_struct?: number; // Mẫu phân tích dữ liệu

  // FilterByObjectTab fields (API field names)
  ma_bp?: string | null; // Mã bộ phận (UUID)
  ma_vv?: string | null; // Mã vụ việc (UUID)
  ma_hd?: string | null; // Mã hợp đồng (UUID)
  ma_dtt?: string | null; // Mã đợt thanh toán (UUID)
  ma_ku?: string | null; // Mã khế ước (UUID)
  ma_phi?: string | null; // Mã phí (UUID)
  ma_sp?: string | null; // Mã sản phẩm (UUID)
  ma_lsx?: string | null; // Mã lệnh sản xuất (UUID)
  ma_cp0?: string | null; // Mã chi phí không hợp lệ (UUID)

  // OtherTab fields (API field names)
  ma_gd?: string | null; // Mã giao dịch (UUID)
  tk_vt?: string | null; // Tài khoản vật tư (UUID)
  ma_nvbh?: string | null; // Mã nhân viên bán hàng (UUID)
  tk_dt?: string | null; // Tài khoản doanh thu (UUID)
  tk_gv?: string | null; // Tài khoản giá vốn (UUID)
  ma_lo?: string | null; // Mã lô (UUID)
  ma_vi_tri?: string | null; // Mã vị trí (UUID)
  so_ct1?: string | null; // Số chứng từ từ
  so_ct2?: string | null; // Số chứng từ đến
  dien_giai?: string | null; // Diễn giải

  // Legacy fields for backward compatibility (will be mapped to API fields)
  customerCode?: string | null;
  customerGroup1?: string | null;
  customerGroup2?: string | null;
  customerGroup3?: string | null;
  region?: string | null;
  productCode?: string | null;
  productType?: string | null;
  trackInventory?: boolean;
  productGroup1?: string | null;
  productGroup2?: string | null;
  productGroup3?: string | null;
  warehouseCode?: string | null;
  department?: string | null;
  project?: string | null;
  contract?: string | null;
  paymentPhase?: string | null;
  agreement?: string | null;
  fee?: string | null;
  product?: string | null;
  productionOrder?: string | null;
  invalidExpense?: string | null;
  transactionCode?: string | null;
  itemAccount?: string | null;
  employeeCode?: string | null;
  revenueAccount?: string | null;
  costAccount?: string | null;
  batchCode?: string | null;
  locationCode?: string | null;
  fromDocumentNumber?: string | null;
  toDocumentNumber?: string | null;
  description?: string | null;

  // Additional fields
  metric?: string;
  dataAnalysisTemplate?: string;
  reportTemplate?: string;
  reportFilterTemplate?: string;
  loai_hang_yn?: boolean;
  includeProductsAndServices?: boolean;

  // Data objects for form integration
  customerCode_data?: any;
  customerGroup1_data?: any;
  customerGroup2_data?: any;
  customerGroup3_data?: any;
  region_data?: any;
  productCode_data?: any;
  productGroup1_data?: any;
  productGroup2_data?: any;
  productGroup3_data?: any;
  warehouseCode_data?: any;
  department_data?: any;
  project_data?: any;
  contract_data?: any;
  paymentPhase_data?: any;
  agreement_data?: any;
  fee_data?: any;
  product_data?: any;
  productionOrder_data?: any;
  invalidExpense_data?: any;
  transactionCode_data?: any;
  itemAccount_data?: any;
  employeeCode_data?: any;
  revenueAccount_data?: any;
  costAccount_data?: any;
  batchCode_data?: any;
  locationCode_data?: any;

  // Pagination
  page?: number;
  page_size?: number;
}

// Individual report item interface (based on actual API response)
export interface MultiPeriodSalesItem {
  id?: string;

  // Main grouping fields (based on detail_by parameter)
  nhom?: string; // Nhóm chính
  ma?: string; // Mã (khách hàng, vật tư, etc.)
  ten?: string; // Tên (khách hàng, vật tư, etc.)

  // Legacy fields for backward compatibility
  ma_kh?: string; // Mã khách hàng
  ten_kh?: string; // Tên khách hàng
  ma_vt?: string; // Mã vật tư
  ten_vt?: string; // Tên vật tư
  ma_kho?: string; // Mã kho
  ten_kho?: string; // Tên kho

  // Period data - dynamic columns based on periods
  // Format: "01-2025", "02-2025", "03-2025", etc.
  [key: string]: any; // For dynamic period columns

  // Summary fields
  tong_cong?: number; // Tổng cộng
  ty_le?: number; // Tỷ lệ %

  // Additional organizational fields
  ma_bp?: string; // Mã bộ phận
  ma_vv?: string; // Mã vụ việc/nhân viên
  ma_unit?: string; // Mã đơn vị

  // System fields
  systotal?: string;
  line?: number;
  syspivot?: string;
  sysorder?: number;
  sysprint?: string;
}

// API response interface
export interface MultiPeriodSalesResponse {
  results: MultiPeriodSalesItem[];
  count: number;
  next?: string;
  previous?: string;
  summary?: {
    // Summary totals for each period
    [key: string]: number;
  } & {
    total_tong_cong?: number;
  };
  periods?: string[]; // List of period labels
  report_structure?: {
    template_type: string;
    columns: Array<{
      field: string;
      headerName: string;
      width?: number;
      type?: string;
    }>;
  };
}

// Hook return type
export interface UseMultiPeriodSalesReturn {
  data: MultiPeriodSalesItem[];
  isLoading: boolean;
  error: Error | null;
  totalItems: number;
  currentPage: number;
  pageSize: number;
  periods: string[];
  reportStructure: any;
  fetchData: (searchParams: MultiPeriodSalesSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
  postData: (searchParams: MultiPeriodSalesSearchFormValues) => Promise<void>;
  handlePageChange: (page: number) => Promise<void>;
}

// Export types for external use
export type {
  MultiPeriodSalesItem as MultiPeriodSalesReportItem,
  MultiPeriodSalesResponse as MultiPeriodSalesReportResponse,
  UseMultiPeriodSalesReturn as UseMultiPeriodSalesReportDataReturn
};
