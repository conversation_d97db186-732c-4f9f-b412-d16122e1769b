import { useState, useCallback } from 'react';
import { MultiPeriodSalesSearchFormValues } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

export const useBaoCaoBanHangNhieuKy = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { entity } = useAuth();

  const submitBaoCaoBanHangNhieuKy = useCallback(
    async (data: MultiPeriodSalesSearchFormValues): Promise<any> => {
      if (!entity?.slug) throw new Error('Entity not found');

      setIsLoading(true);
      try {
        const response = await api.post(
          `/entities/${entity.slug}/erp/ban-hang/bao-cao-ban-hang/bao-cao-ban-hang-nhieu-ky/`,
          data
        );
        return response.data;
      } catch (error) {
        console.error('Error submitting multi-period sales report:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    isLoading,
    submitBaoCaoBanHangNhieuKy
  };
};
