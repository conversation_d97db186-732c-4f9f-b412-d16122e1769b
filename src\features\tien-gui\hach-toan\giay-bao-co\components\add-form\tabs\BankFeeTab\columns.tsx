import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  phiNganHangSearchColumns,
  QUERY_KEYS,
  taiKhoanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  lenhSanXuatSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  boPhanSearchColumns
} from '@/constants';
import {
  KhachHang,
  PhiNganHang,
  TaiKhoan,
  Tax,
  VuViec,
  HopDong,
  DotThanhToan,
  KheUoc,
  Phi,
  VatTu
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getBankFeeColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
  },
  {
    field: 'ma_cpnh',
    headerName: 'Mã chi phí',
    width: 120,
    renderCell: params => (
      <SearchField<PhiNganHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI_NGAN_HANG}/`}
        searchColumns={phiNganHangSearchColumns}
        columnDisplay='ma_cpnh'
        dialogTitle='Danh mục phí ngân hàng'
        value={params.row.ma_cpnh_data?.ma_cpnh || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cpnh_data', row)}
      />
    )
  },
  {
    field: 'ten_cpnh',
    headerName: 'Tên chi phí ngân hàng',
    width: 180,
    renderCell: params => <CellField name='ten_cpnh' type='text' value={params.row.ma_cpnh_data?.ten_cpnh || ''} />
  },
  {
    field: 'tien_cp_nt',
    headerName: 'Phí VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='tien_cp_nt'
        type='number'
        value={params.row.tien_cp_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'tien_cp_nt', newValue)}
      />
    )
  },
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 120,
    renderCell: params => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 100,
    renderCell: params => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 140,
    renderCell: params => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 240,
    renderCell: params => (
      <CellField
        name='description'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'tk_cpnh',
    headerName: 'Tk phí',
    width: 100,
    renderCell: params => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={taiKhoanSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_cpnh_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_cpnh_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 120,
    renderCell: params => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={taiKhoanSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 'tk_thue',
    headerName: 'TK thuế',
    width: 100,
    renderCell: params => (
      <CellField name='tk_thue' type='text' value={params.row.ma_cpnh_data?.tk_cpnh_data?.code || ''} />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 140,
    renderCell: params => (
      <CellField name='ma_thue' type='text' value={params.row.ma_cpnh_data?.ma_thue_data?.ten_thue || ''} />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 130,
    renderCell: params => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 130,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cpkhl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp0 || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
