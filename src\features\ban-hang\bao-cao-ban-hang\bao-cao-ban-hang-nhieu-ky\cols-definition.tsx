import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// Helper function to render cells with bold formatting for summary rows
const renderCellWithBold = (params: GridRenderCellParams) => {
  const isSummaryRow = params.row.systotal === 'TOTAL' || params.row.id === 'SUMMARY_ROW';
  return <span className={isSummaryRow ? 'font-bold text-blue-800' : ''}>{params.value}</span>;
};

// Helper function to render number cells with formatting
const renderNumberCell = (params: GridRenderCellParams) => {
  const isSummaryRow = params.row.systotal === 'TOTAL' || params.row.id === 'SUMMARY_ROW';
  const value = params.value;

  // Format number with commas
  const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;

  return <span className={isSummaryRow ? 'font-bold text-blue-800' : ''}>{formattedValue}</span>;
};

/**
 * Generate dynamic columns for multi-period sales report
 * @param periods - Array of period strings like ['07-2025', '08-2025']
 * @param data - Data array to check for additional columns
 */
export const multiPeriodSalesColumns = (periods: string[] = [], data: any[] = []): GridColDef[] => {
  // Base columns that match the API response structure
  const baseColumns: GridColDef[] = [
    {
      field: 'ma',
      headerName: 'Mã',
      width: 150,
      renderCell: renderCellWithBold
    },
    {
      field: 'ten',
      headerName: 'Tên',
      width: 300,
      renderCell: renderCellWithBold
    }
  ];

  // Add period columns dynamically
  const periodColumns: GridColDef[] = periods.map(period => ({
    field: period,
    headerName: period,
    width: 120,
    type: 'number',
    renderCell: renderNumberCell,
    align: 'right',
    headerAlign: 'right'
  }));

  // Add total column if exists in data
  const totalColumns: GridColDef[] =
    data.length > 0 && Object.prototype.hasOwnProperty.call(data[0], 'tong_cong')
      ? [
          {
            field: 'tong_cong',
            headerName: 'Tổng cộng',
            width: 150,
            type: 'number',
            renderCell: renderNumberCell,
            align: 'right',
            headerAlign: 'right'
          }
        ]
      : [];

  return [...baseColumns, ...periodColumns, ...totalColumns];
};

/**
 * Fallback columns when no periods are detected
 */
export const fallbackColumns: GridColDef[] = [
  {
    field: 'ma',
    headerName: 'Mã',
    width: 150,
    renderCell: renderCellWithBold
  },
  {
    field: 'ten',
    headerName: 'Tên',
    width: 300,
    renderCell: renderCellWithBold
  },
  {
    field: '07-2025',
    headerName: '07-2025',
    width: 120,
    type: 'number',
    renderCell: renderNumberCell,
    align: 'right',
    headerAlign: 'right'
  }
];
