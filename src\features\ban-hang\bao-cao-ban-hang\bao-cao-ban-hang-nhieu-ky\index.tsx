'use client';

import * as React from 'react';
import { ActionBar, InitialSearchDialog, EditPrintTemplateDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useDialogState, useActionHandlers } from './hooks';
import { useTableData } from './hooks/useTableData';

export default function MultiSalesReportPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick, isLoading, error, refreshData, totalItems, currentPage, handlePageChange } =
    useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  // Extract report template and date range from searchParams
  const reportTemplate = searchParams?.data_analysis_struct?.toString();
  const dateRange = {
    startDate: searchParams?.ngay_ct1?.toString(),
    endDate: searchParams?.ngay_ct2?.toString()
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={() => refreshData()}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            reportTemplate={reportTemplate}
            dateRange={dateRange}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-lg'>Đang tải dữ liệu...</div>
              </div>
            ) : error ? (
              <div className='flex h-full items-center justify-center'>
                <div className='text-lg text-red-500'>Lỗi: {error.message}</div>
              </div>
            ) : (
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                getRowId={row => row.id || row.ma || `${row.ma}-${Math.random()}`}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
