import { z } from 'zod';

export const searchSchema = z.object({
  // BasicInfo fields
  pivot_by: z.string().min(1, '<PERSON><PERSON> tích theo không được để trống'),
  ngay_ct1: z.string().min(1, '<PERSON><PERSON><PERSON> bắt đầu không được để trống'),
  ngay_ct2: z.string().min(1, '<PERSON><PERSON><PERSON> kết thúc không được để trống'),
  so_ky: z.union([z.string(), z.number()]).transform(val => Number(val)),
  detail_by: z.string().min(1, '<PERSON><PERSON><PERSON> cáo theo không được để trống'),
  group_by: z.string().min(1, 'Nhóm theo không được để trống'),

  // API field names - Customer fields
  ma_kh: z.string().optional(),
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  rg_code: z.string().optional(),

  // API field names - Product fields
  ma_vt: z.string().optional(),
  ma_lvt: z.string().optional(),
  ton_kho_yn: z.boolean().optional(),
  nh_vt1: z.string().optional(),
  nh_vt2: z.string().optional(),
  nh_vt3: z.string().optional(),
  ma_kho: z.string().optional(),
  ma_unit: z.string().optional(),
  nh_ct: z.string().optional(),

  // Structure fields
  loai_du_lieu: z
    .union([z.string(), z.number()])
    .transform(val => Number(val))
    .optional(),
  mau_bc: z
    .union([z.string(), z.number()])
    .transform(val => Number(val))
    .optional(),
  data_analysis_struct: z
    .union([z.string(), z.number()])
    .transform(val => Number(val))
    .optional(),

  // API field names - FilterByObjectTab fields
  ma_bp: z.string().optional(),
  ma_vv: z.string().optional(),
  ma_hd: z.string().optional(),
  ma_dtt: z.string().optional(),
  ma_ku: z.string().optional(),
  ma_phi: z.string().optional(),
  ma_sp: z.string().optional(),
  ma_lsx: z.string().optional(),
  ma_cp0: z.string().optional(),

  // API field names - OtherTab fields
  ma_gd: z.string().optional(),
  tk_vt: z.string().optional(),
  ma_nvbh: z.string().optional(),
  tk_dt: z.string().optional(),
  tk_gv: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  dien_giai: z.string().optional(),

  // Legacy fields for backward compatibility
  customerCode: z.string().optional(),
  customerGroup1: z.string().optional(),
  customerGroup2: z.string().optional(),
  customerGroup3: z.string().optional(),
  region: z.string().optional(),
  productCode: z.string().optional(),
  productType: z.string().optional(),
  productGroup1: z.string().optional(),
  productGroup2: z.string().optional(),
  productGroup3: z.string().optional(),
  warehouseCode: z.string().optional(),
  department: z.string().optional(),
  project: z.string().optional(),
  contract: z.string().optional(),
  paymentPhase: z.string().optional(),
  agreement: z.string().optional(),
  fee: z.string().optional(),
  product: z.string().optional(),
  productionOrder: z.string().optional(),
  invalidExpense: z.string().optional(),
  transactionCode: z.string().optional(),
  itemAccount: z.string().optional(),
  employeeCode: z.string().optional(),
  revenueAccount: z.string().optional(),
  costAccount: z.string().optional(),
  batchCode: z.string().optional(),
  locationCode: z.string().optional(),
  fromDocumentNumber: z.string().optional(),
  toDocumentNumber: z.string().optional(),
  description: z.string().optional(),

  // Additional fields
  includeProductsAndServices: z.boolean().optional(),
  trackInventory: z.boolean().optional(),
  reportTemplate: z.string().optional(),
  reportFilterTemplate: z.string().optional(),
  dataAnalysisTemplate: z.string().optional(),
  metric: z.string().optional(),
  loai_hang_yn: z.boolean().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // BasicInfo fields
  pivot_by: 'M',
  ngay_ct1: new Date().toISOString().split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  so_ky: 12,
  detail_by: '200',
  group_by: '__all__',

  // API field names - Customer fields
  ma_kh: '',
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  rg_code: '',

  // API field names - Product fields
  ma_vt: '',
  ma_lvt: '',
  ton_kho_yn: false,
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ma_kho: '',
  ma_unit: '',
  nh_ct: '',

  // Structure fields
  loai_du_lieu: 1,
  mau_bc: 20,
  data_analysis_struct: 1,

  // API field names - FilterByObjectTab fields
  ma_bp: '',
  ma_vv: '',
  ma_hd: '',
  ma_dtt: '',
  ma_ku: '',
  ma_phi: '',
  ma_sp: '',
  ma_lsx: '',
  ma_cp0: '',

  // API field names - OtherTab fields
  ma_gd: '',
  tk_vt: '',
  ma_nvbh: '',
  tk_dt: '',
  tk_gv: '',
  ma_lo: '',
  ma_vi_tri: '',
  so_ct1: '',
  so_ct2: '',
  dien_giai: '',

  // Legacy fields for backward compatibility
  customerCode: '',
  customerGroup1: '',
  customerGroup2: '',
  customerGroup3: '',
  region: '',
  productCode: '',
  productType: '',
  productGroup1: '',
  productGroup2: '',
  productGroup3: '',
  warehouseCode: '',
  department: '',
  project: '',
  contract: '',
  paymentPhase: '',
  agreement: '',
  fee: '',
  product: '',
  productionOrder: '',
  invalidExpense: '',
  transactionCode: '',
  itemAccount: '',
  employeeCode: '',
  revenueAccount: '',
  costAccount: '',
  batchCode: '',
  locationCode: '',
  fromDocumentNumber: '',
  toDocumentNumber: '',
  description: '',

  // Additional fields
  includeProductsAndServices: false,
  trackInventory: false,
  reportTemplate: 'quantity',
  reportFilterTemplate: '',
  dataAnalysisTemplate: 'no_analysis',
  metric: 'sl_xuat',
  loai_hang_yn: false
};
