import { useState, useEffect, useCallback } from 'react';
import {
  GiayBaoCo,
  GiayBaoCoInput,
  GiayBaoCoResponse,
  ChiTietGiayBaoCo,
  ChiTietGiayBaoCoInput,
  PhieuNganHangGiayBaoCo,
  PhieuNganHangGiayBaoCoInput
} from '@/types/schemas/giay-bao-co.type';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseGiayBaoCoReturn {
  giayBaoCos: GiayBaoCo[];
  isLoading: boolean;
  isLoadingDetail: boolean;
  addGiayBaoCo: (newGiayBaoCo: GiayBaoCoInput) => Promise<GiayBaoCo>;
  updateGiayBaoCo: (uuid: string, updatedGiayBaoCo: GiayBaoCoInput) => Promise<GiayBaoCo>;
  deleteGiayBaoCo: (uuid: string) => Promise<void>;
  refreshGiayBaoCos: () => Promise<void>;
  getGiayBaoCoById: (uuid: string) => Promise<GiayBaoCo | null>;
  getGiayBaoCoByCustomer: (customerId: string) => Promise<GiayBaoCo[]>;
  getGiayBaoCoByStatus: (status: string) => Promise<GiayBaoCo[]>;
  getGiayBaoCoByDateRange: (fromDate: string, toDate: string) => Promise<GiayBaoCo[]>;
  getGiayBaoCoDetail: (uuid: string) => Promise<ChiTietGiayBaoCo[]>;
  // Chi tiet methods
  addChiTietGiayBaoCo: (giayBaoCoUuid: string, newChiTiet: ChiTietGiayBaoCoInput) => Promise<ChiTietGiayBaoCo>;
  updateChiTietGiayBaoCo: (
    giayBaoCoUuid: string,
    chiTietUuid: string,
    updatedChiTiet: ChiTietGiayBaoCoInput
  ) => Promise<ChiTietGiayBaoCo>;
  deleteChiTietGiayBaoCo: (giayBaoCoUuid: string, chiTietUuid: string) => Promise<void>;
  getChiTietGiayBaoCo: (giayBaoCoUuid: string) => Promise<ChiTietGiayBaoCo[]>;
  // Phieu ngan hang methods
  addPhieuNganHangGiayBaoCo: (
    giayBaoCoUuid: string,
    newPhieuNganHang: PhieuNganHangGiayBaoCoInput
  ) => Promise<PhieuNganHangGiayBaoCo>;
  updatePhieuNganHangGiayBaoCo: (
    giayBaoCoUuid: string,
    phieuNganHangUuid: string,
    updatedPhieuNganHang: PhieuNganHangGiayBaoCoInput
  ) => Promise<PhieuNganHangGiayBaoCo>;
  deletePhieuNganHangGiayBaoCo: (giayBaoCoUuid: string, phieuNganHangUuid: string) => Promise<void>;
  getPhieuNganHangGiayBaoCo: (giayBaoCoUuid: string) => Promise<PhieuNganHangGiayBaoCo[]>;
}

/**
 * Hook for managing GiayBaoCo (Bank Credit Advice) data
 *
 * This hook provides functions to fetch, create, update, and delete bank credit advice documents,
 * as well as manage their details and bank fee information.
 */
export const useGiayBaoCo = (initialGiayBaoCos: GiayBaoCo[] = []): UseGiayBaoCoReturn => {
  const [giayBaoCos, setGiayBaoCos] = useState<GiayBaoCo[]>(initialGiayBaoCos);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(false);

  const { entity } = useAuth();

  const fetchGiayBaoCos = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<GiayBaoCoResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/`);
      setGiayBaoCos(response.data.results);
    } catch (error) {
      console.error('Error fetching bank credit advice documents:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  /**
   * Get GiayBaoCo by ID
   */
  const getGiayBaoCoById = async (uuid: string): Promise<GiayBaoCo | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get<GiayBaoCo>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching bank credit advice with UUID ${uuid}:`, error);
      return null;
    }
  };

  /**
   * Get GiayBaoCo by customer
   */
  const getGiayBaoCoByCustomer = async (customerId: string): Promise<GiayBaoCo[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<GiayBaoCoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching bank credit advice by customer:', error);
      return [];
    }
  };

  /**
   * Get GiayBaoCo by status
   */
  const getGiayBaoCoByStatus = async (status: string): Promise<GiayBaoCo[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<GiayBaoCoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/?status=${status}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching bank credit advice by status:', error);
      return [];
    }
  };

  /**
   * Get GiayBaoCo by date range
   */
  const getGiayBaoCoByDateRange = async (fromDate: string, toDate: string): Promise<GiayBaoCo[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<GiayBaoCoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/?ngay_ct_from=${fromDate}&ngay_ct_to=${toDate}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching bank credit advice by date range:', error);
      return [];
    }
  };

  /**
   * Add new GiayBaoCo
   */
  const addGiayBaoCo = async (newGiayBaoCo: GiayBaoCoInput): Promise<GiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.post<GiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/`,
        newGiayBaoCo
      );
      setGiayBaoCos(prev => [response.data, ...prev]);
      return response.data;
    } catch (error) {
      console.error('Error adding bank credit advice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update GiayBaoCo
   */
  const updateGiayBaoCo = async (uuid: string, updatedGiayBaoCo: GiayBaoCoInput): Promise<GiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.put<GiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${uuid}/`,
        updatedGiayBaoCo
      );
      setGiayBaoCos(prev => prev.map(item => (item.uuid === uuid ? response.data : item)));
      return response.data;
    } catch (error) {
      console.error('Error updating bank credit advice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete GiayBaoCo
   */
  const deleteGiayBaoCo = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${uuid}/`);
      setGiayBaoCos(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting bank credit advice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get GiayBaoCo detail
   */
  const getGiayBaoCoDetail = async (uuid: string): Promise<ChiTietGiayBaoCo[]> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.get<ChiTietGiayBaoCo[]>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${uuid}/`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching bank credit advice detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  /**
   * Add ChiTietGiayBaoCo
   */
  const addChiTietGiayBaoCo = async (
    giayBaoCoUuid: string,
    newChiTiet: ChiTietGiayBaoCoInput
  ): Promise<ChiTietGiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.post<ChiTietGiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/chi-tiet/`,
        newChiTiet
      );
      return response.data;
    } catch (error) {
      console.error('Error adding bank credit advice detail:', error);
      throw error;
    }
  };

  /**
   * Update ChiTietGiayBaoCo
   */
  const updateChiTietGiayBaoCo = async (
    giayBaoCoUuid: string,
    chiTietUuid: string,
    updatedChiTiet: ChiTietGiayBaoCoInput
  ): Promise<ChiTietGiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.put<ChiTietGiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/chi-tiet/${chiTietUuid}/`,
        updatedChiTiet
      );
      return response.data;
    } catch (error) {
      console.error('Error updating bank credit advice detail:', error);
      throw error;
    }
  };

  /**
   * Delete ChiTietGiayBaoCo
   */
  const deleteChiTietGiayBaoCo = async (giayBaoCoUuid: string, chiTietUuid: string): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.delete(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/chi-tiet/${chiTietUuid}/`
      );
    } catch (error) {
      console.error('Error deleting bank credit advice detail:', error);
      throw error;
    }
  };

  /**
   * Get ChiTietGiayBaoCo
   */
  const getChiTietGiayBaoCo = async (giayBaoCoUuid: string): Promise<ChiTietGiayBaoCo[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<ChiTietGiayBaoCo[]>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/chi-tiet/`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching bank credit advice details:', error);
      return [];
    }
  };

  /**
   * Add PhieuNganHangGiayBaoCo
   */
  const addPhieuNganHangGiayBaoCo = async (
    giayBaoCoUuid: string,
    newPhieuNganHang: PhieuNganHangGiayBaoCoInput
  ): Promise<PhieuNganHangGiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.post<PhieuNganHangGiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/phieu-ngan-hang/`,
        newPhieuNganHang
      );
      return response.data;
    } catch (error) {
      console.error('Error adding bank fee for credit advice:', error);
      throw error;
    }
  };

  /**
   * Update PhieuNganHangGiayBaoCo
   */
  const updatePhieuNganHangGiayBaoCo = async (
    giayBaoCoUuid: string,
    phieuNganHangUuid: string,
    updatedPhieuNganHang: PhieuNganHangGiayBaoCoInput
  ): Promise<PhieuNganHangGiayBaoCo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.put<PhieuNganHangGiayBaoCo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/phieu-ngan-hang/${phieuNganHangUuid}/`,
        updatedPhieuNganHang
      );
      return response.data;
    } catch (error) {
      console.error('Error updating bank fee for credit advice:', error);
      throw error;
    }
  };

  /**
   * Delete PhieuNganHangGiayBaoCo
   */
  const deletePhieuNganHangGiayBaoCo = async (giayBaoCoUuid: string, phieuNganHangUuid: string): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.delete(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/phieu-ngan-hang/${phieuNganHangUuid}/`
      );
    } catch (error) {
      console.error('Error deleting bank fee for credit advice:', error);
      throw error;
    }
  };

  /**
   * Get PhieuNganHangGiayBaoCo
   */
  const getPhieuNganHangGiayBaoCo = async (giayBaoCoUuid: string): Promise<PhieuNganHangGiayBaoCo[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<PhieuNganHangGiayBaoCo[]>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${giayBaoCoUuid}/phieu-ngan-hang/`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching bank fees for credit advice:', error);
      return [];
    }
  };

  /**
   * Refresh GiayBaoCos data
   */
  const refreshGiayBaoCos = async (): Promise<void> => {
    await fetchGiayBaoCos();
  };

  useEffect(() => {
    fetchGiayBaoCos();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    giayBaoCos,
    isLoading,
    isLoadingDetail,
    addGiayBaoCo,
    updateGiayBaoCo,
    deleteGiayBaoCo,
    refreshGiayBaoCos,
    getGiayBaoCoById,
    getGiayBaoCoByCustomer,
    getGiayBaoCoByStatus,
    getGiayBaoCoByDateRange,
    getGiayBaoCoDetail,
    addChiTietGiayBaoCo,
    updateChiTietGiayBaoCo,
    deleteChiTietGiayBaoCo,
    getChiTietGiayBaoCo,
    addPhieuNganHangGiayBaoCo,
    updatePhieuNganHangGiayBaoCo,
    deletePhieuNganHangGiayBaoCo,
    getPhieuNganHangGiayBaoCo
  };
};
