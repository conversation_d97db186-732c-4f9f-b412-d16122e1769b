import {
  MA_CHUNG_TU,
  QUERY_KEYS,
  accountSearchColumns,
  hanThanhToanSearchColumns,
  khachHangSearchColumns,
  nhanVienSearchColumns,
  quyenChungTuSearchColumns,
  taiKhoanNganHangSearchColumns
} from '@/constants';
import {
  SearchField,
  FormField,
  AritoIcon,
  UnitDropdown,
  DocumentNumberField,
  CurrencyInput
} from '@/components/custom/arito';
import { AccountModel, HanThanhToan, KhachHang, NhanVien, QuyenChungTu, type TaiKhoanNganHang } from '@/types/schemas';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useNgoaiTe } from '@/hooks';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: any;
    actions: any;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Customer Information Section */}
      <div className='flex gap-4'>
        {/* Left Column */}
        <div className='flex basis-1/2 flex-col'>
          <div className='w-full'>
            <div className='flex items-center gap-2'>
              <Label className='w-32 min-w-32 text-left'>Loại chứng từ</Label>
              <div className='max-w-md flex-1'>
                <FormField
                  type='select'
                  options={[
                    { value: '1', label: '1. Chi theo hóa đơn' },
                    { value: '2', label: '2. Chi theo đối tượng' },
                    { value: '3', label: '3. Chi khác' },
                    { value: '4', label: '4. Rút tiền về nhập quỹ' },
                    { value: '5', label: '5. Chuyển giữa các ngân hàng' }
                  ]}
                  name='ma_ngv'
                  value={state.loaiChungTu}
                  onValueChange={actions.setLoaiChungTu}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Địa chỉ</Label>
            <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Người nhận tiền</Label>
            <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
          </div>

          {/* Description Field */}
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Diễn giải</Label>
            <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
          </div>

          {/* Bank and Account Fields */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32'>Ngân hàng</Label>
            <div className='flex gap-2'>
              <SearchField<TaiKhoanNganHang>
                type='text'
                value={state.bank?.account_code || ''}
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}`}
                searchColumns={taiKhoanNganHangSearchColumns}
                columnDisplay='account_code'
                dialogTitle='Danh mục tài khoản ngân hàng'
                onRowSelection={actions.setBank}
                relatedFieldValue={state.bank?.name || ''}
                disabled={formMode === 'view'}
              />
              <div className='flex items-center gap-2'>
                <Label className='whitespace-nowrap'>Tài khoản có</Label>
                <SearchField<AccountModel>
                  type='text'
                  value={state.account?.code || ''}
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                  searchColumns={accountSearchColumns}
                  columnDisplay='code'
                  displayRelatedField='name'
                  dialogTitle='Danh mục tài khoản'
                  onRowSelection={actions.setAccount}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className='flex basis-1/2 justify-end'>
          <div className='w-80'>
            {/* Tab Content */}
            <div className='flex-1 pr-2'>
              <div>
                <div className='flex items-center'>
                  <DocumentNumberField
                    ma_ct={MA_CHUNG_TU.TIEN_GUI.GIAY_BAO_NO}
                    quyenChungTu={state.quyenChungTu}
                    onQuyenChungTuChange={actions.setQuyenChungTu}
                    soChungTu={state.soChungTu || ''}
                    onSoChungTuChange={actions.setSoChungTu}
                    disabled={formMode === 'view'}
                    classNameSearchField='w-full'
                  />
                </div>
                <div className='flex items-center'>
                  <Label className='w-32'>Ngày chứng từ</Label>
                  <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
                </div>
                <div className='flex items-center'>
                  <Label className='w-32'>Ngày lập chứng từ</Label>
                  <FormField name='ngay_lct' type='date' disabled={formMode === 'view'} />
                </div>

                <div className='flex'>
                  <CurrencyInput formMode={formMode} classNameInput='w-full' />
                </div>
                <div className='flex items-center'>
                  <Label className='w-32'>Trạng thái</Label>
                  <FormField
                    name='status'
                    type='select'
                    className='min-w-40'
                    disabled={formMode === 'view'}
                    options={[
                      { value: '0', label: 'Lập chứng từ' },
                      { value: '3', label: 'Chờ duyệt' },
                      { value: '5', label: 'Xuất hóa đơn' },
                      { value: '7', label: 'Bỏ duyệt đơn hàng' }
                    ]}
                  />
                </div>

                <div className='mt-2 flex'>
                  <div className='mb-4 h-2 w-32 shrink-0' />
                  <FormField
                    label='Dữ liệu nhận được'
                    name='transfer_yn'
                    type='checkbox'
                    disabled={true}
                    labelClassName='w-32'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
