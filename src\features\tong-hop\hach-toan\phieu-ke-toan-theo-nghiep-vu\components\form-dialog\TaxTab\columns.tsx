import { GridColDef } from '@mui/x-data-grid';
import {
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  QUERY_KEYS,
  thueSearchColumns,
  tinhChatThueSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  mauSoHoaDonSearchColumns,
  hanThanhToanSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  ChiPhiKhongHopLeData,
  ChungTu,
  DoiTuong,
  DotThanhToan,
  HanThanhToan,
  HopDong,
  KhachHang,
  KheUoc,
  MauSoHoaDon,
  Phi,
  Tax,
  TinhChatThue,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito/form';

export const getTaxColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hoá đơn',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<Tax>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        dialogTitle='Danh mục thuế'
        columnDisplay='ma_thue'
        displayRelatedField='ten_thue'
        value={params.row.ma_thue || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_thue_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_mau_ct',
    headerName: 'Mẫu hoá đơn',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<MauSoHoaDon>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
        searchColumns={mauSoHoaDonSearchColumns}
        dialogTitle='Danh mục mẫu hoá đơn'
        columnDisplay='ma_mau_so'
        value={params.row.ma_mau_so_data?.ma_mau_so || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_mau_so_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ma_mau_bc'
        type='select'
        options={[
          { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
          { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
          { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
        ]}
        value={params.row.ma_mau_bc || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
      />
    )
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã TC thuế',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<TinhChatThue>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TINH_CHAT_THUE}/`}
        searchColumns={tinhChatThueSearchColumns}
        dialogTitle='Danh mục tính chất thuế'
        columnDisplay='ma_tc_thue'
        value={params.row.ma_tc_thue_data?.ma_tc_thue || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_tc_thue_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã ncc',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        dialogTitle='Danh mục khách hàng'
        columnDisplay='customer_code'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_kh_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 200,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ten_kh_thue'
        type='text'
        value={params.row.ma_kh_data?.customer_name || params.row.ten_kh_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
      />
    )
  },
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 200,
    renderCell: (params: { row: any }) => (
      <CellField
        name='dia_chi'
        type='text'
        value={params.row.ma_kh_data?.address || params.row.dia_chi}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
      />
    )
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ma_so_thue'
        type='text'
        value={params.row.ma_kh_data?.tax_code || params.row.ma_so_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
      />
    )
  },
  {
    field: 'ten_vt_thue',
    headerName: 'Tên hàng hoá - dịch vụ',
    width: 200,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ten_vt_thue'
        type='text'
        value={params.row.ten_vt_thue}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
      />
    )
  },
  {
    field: 't_tien_nt',
    headerName: 'Tiền hàng VND',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='t_tien_nt'
        type='number'
        value={params.row.t_tien_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_tien_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_thue_no',
    headerName: 'TK thuế',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_thue_no_data?.code || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'tk_thue_no_data', row);
        }}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'TK đối ứng',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'tk_du_data', row);
        }}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: (params: { row: any }) => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_kh9',
    headerName: 'Cục thuế',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        dialogTitle='Danh mục đối tượng'
        columnDisplay='customer_code'
        value={params.row.ma_kh9_data?.customer_code || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_kh9_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_tt',
    headerName: 'Mã thanh toán',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<HanThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        searchColumns={hanThanhToanSearchColumns}
        dialogTitle='Danh mục thanh toán'
        columnDisplay='ma_tt'
        value={params.row.ma_tt_data?.ma_tt || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_tt_data', row);
        }}
      />
    )
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: (params: { row: any }) => (
      <CellField
        name='ghi_chu'
        type='text'
        value={params.row.ghi_chu}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        dialogTitle='Danh mục bộ phận'
        columnDisplay='ma_bp'
        displayRelatedField='ten_bp'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_bp_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
        searchColumns={vuViecSearchColumns}
        dialogTitle='Danh mục vụ việc'
        columnDisplay='ma_vu_viec'
        displayRelatedField='ten_vu_viec'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_vv_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Mã hợp đồng',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        dialogTitle='Danh mục hợp đồng'
        columnDisplay='ma_hd'
        displayRelatedField='ten_hd'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_hd_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        dialogTitle='Danh mục đợt thanh toán'
        columnDisplay='ma_dtt'
        displayRelatedField='ten_dtt'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_dtt_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
        searchColumns={kheUocSearchColumns}
        dialogTitle='Danh mục khế ước'
        columnDisplay='ma_ku'
        displayRelatedField='ten_ku'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_ku_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}`}
        searchColumns={phiSearchColumns}
        dialogTitle='Danh mục phí'
        columnDisplay='ma_phi'
        displayRelatedField='ten_phi'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_phi_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: (params: { row: any }) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
        searchColumns={vatTuSearchColumns}
        dialogTitle='Danh mục sản phẩm'
        columnDisplay='ma_vt'
        displayRelatedField='ten_vt'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_sp_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: { row: any }) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        dialogTitle='Danh mục lệnh sản xuất'
        columnDisplay='ma_lsx'
        displayRelatedField='ten_lsx'
        value={params.row.ma_lsx || ''}
        onRowSelection={row => {
          onCellValueChange(params.row.uuid, 'ma_lsx_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 180,
    renderCell: (params: { row: any }) => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
