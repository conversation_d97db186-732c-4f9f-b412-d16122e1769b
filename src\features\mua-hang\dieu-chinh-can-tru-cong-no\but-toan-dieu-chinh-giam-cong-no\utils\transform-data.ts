import { isValidUUID } from '@/lib/uuid-validator';
import { FormFieldState } from '../hooks';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';

export const transformDetailRows = (detailRows: any[], dien_giai: string, formMode: string) => {
  return detailRows.map((row: any, index: number) => {
    const uuid = formMode !== 'add' && isValidUUID(row.uuid) ? { uuid: row.uuid } : {};
    return {
      ...uuid,
      line: index + 1,
      dien_giai: row.dien_giai || dien_giai,

      ma_kh: row.ma_kh_data?.uuid || '',
      tk: row.tk_data?.uuid || '',
      ten_tk: row.tk_data?.name || '',
      ps_no_nt: row.ps_no_nt || 0,
      ps_co_nt: row.ps_co_nt || 0,
      nh_dk: row.nh_dk || '',
      so_ct0: row.so_ct0 || '',
      ngay_ct0: row.ngay_ct0 || '',

      ma_bp: row.ma_bp_data?.uuid || '',
      ma_vv: row.ma_vv_data?.uuid || '',
      ma_hd: row.ma_hd_data?.uuid || '',
      ma_dtt: row.ma_dtt_data?.uuid || '',
      ma_ku: row.ma_ku_data?.uuid || '',
      ma_phi: row.ma_phi_data?.uuid || '',
      ma_sp: row.ma_sp_data?.uuid || '',
      ma_lsx: row.ma_lsx_data?.uuid || '',
      ma_cp0: row.ma_cp0_data?.uuid || ''
    };
  });
};

export const transformFormData = (data: any, state: FormFieldState, formMode: string, ...rest: any[]) => {
  const detail = transformDetailRows(rest[0], data.dien_giai, formMode);

  return {
    ...data,
    ty_gia: (data.ty_gia ? parseFloat(data.ty_gia) : 1).toFixed(2) || 1.0,
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu),

    tk: state.taiKhoan?.uuid || '',
    chi_tiet: detail
  };
};
