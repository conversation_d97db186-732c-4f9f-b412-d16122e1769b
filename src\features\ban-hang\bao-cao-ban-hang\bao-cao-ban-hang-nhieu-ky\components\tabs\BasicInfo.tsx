import { useFormContext } from 'react-hook-form';
import React, { useEffect } from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { calculateEndDate } from '../../utils/dateCalculation';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

const reportByOptions = [
  { value: '200', label: 'Khách hàng' },
  { value: '210', label: 'Nhóm khách 1' },
  { value: '220', label: 'Nhóm khách 2' },
  { value: '230', label: 'Nhóm khách 3' },
  { value: '300', label: 'Vật tư' },
  { value: '320', label: 'Nhóm vật tư 1' },
  { value: '330', label: 'Nhóm vật tư 2' },
  { value: '340', label: '<PERSON><PERSON><PERSON><PERSON> vật tư 3' },
  { value: '700', label: 'Đơn vị' },
  { value: '810', label: 'B<PERSON> phận' },
  { value: '820', label: 'Vụ việc' },
  { value: '830', label: 'Hợp đồng' },
  { value: '840', label: 'Khế ước' },
  { value: '850', label: 'Phí' },
  { value: '860', label: 'Sản phẩm' },
  { value: '870', label: 'Lệnh sản xuất' },
  { value: '910', label: 'Nhân viên bán hàng' }
];
const analysisByOptions = [
  { value: 'D', label: 'Ngày' },
  { value: 'W', label: 'Tuần' },
  { value: 'M', label: 'Tháng' },
  { value: 'Q', label: 'Quý' },
  { value: '6M', label: '6 tháng' },
  { value: 'Y', label: 'Năm' }
];

const groupByOptions = [
  { value: '__all__', label: 'Không nhóm' },
  { value: '200', label: 'Khách hàng' },
  { value: '210', label: 'Nhóm khách 1' },
  { value: '220', label: 'Nhóm khách 2' },
  { value: '230', label: 'Nhóm khách 3' },
  { value: '300', label: 'Vật tư' },
  { value: '320', label: 'Nhóm vật tư 1' },
  { value: '330', label: 'Nhóm vật tư 2' },
  { value: '340', label: 'Nhóm vật tư 3' },
  { value: '700', label: 'Đơn vị' },
  { value: '810', label: 'Bộ phận' },
  { value: '820', label: 'Vụ việc' },
  { value: '830', label: 'Hợp đồng' },
  { value: '840', label: 'Khế ước' },
  { value: '850', label: 'Phí' },
  { value: '860', label: 'Sản phẩm' },
  { value: '870', label: 'Lệnh sản xuất' },
  { value: '910', label: 'Nhân viên bán hàng' }
];

const BasicInfo: React.FC = () => {
  const { watch, setValue } = useFormContext();

  // Watch for changes in the fields that affect ngay_ct2 calculation
  const ngayCt1 = watch('ngay_ct1');
  const soKy = watch('so_ky');
  const pivotBy = watch('pivot_by');

  // Auto-calculate ngay_ct2 when dependencies change
  useEffect(() => {
    if (ngayCt1 && soKy && pivotBy) {
      const numSoKy = typeof soKy === 'string' ? parseInt(soKy, 10) : soKy;
      if (numSoKy > 0) {
        const calculatedEndDate = calculateEndDate(ngayCt1, numSoKy, pivotBy as 'D' | 'W' | 'M' | 'Q' | '6M' | 'Y');
        if (calculatedEndDate) {
          setValue('ngay_ct2', calculatedEndDate);
        }
      }
    }
  }, [ngayCt1, soKy, pivotBy, setValue]);

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phân tích theo:</Label>
          <div>
            <FormField name='pivot_by' type='select' options={analysisByOptions} className='w-48' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày bắt đầu:</Label>
          <div className='flex items-center gap-2'>
            <FormField name='ngay_ct1' type='date' className='w-48' />
            <FormField name='ngay_ct2' type='date' className='w-48' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số kỳ phân tích</Label>
          <FormField name='so_ky' type='text' className='w-48' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Báo cáo theo:</Label>
          <div>
            <FormField name='detail_by' type='select' options={reportByOptions} className='w-48' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo:</Label>
          <div>
            <div className='flex items-center justify-between'>
              <div className='flex-1'>
                <FormField name='group_by' type='select' options={groupByOptions} className='w-48' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
