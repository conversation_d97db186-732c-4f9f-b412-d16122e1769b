import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  accountantSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns
} from '@/constants';
import {
  AccountModel,
  ChiPhi,
  BoPhan,
  ChiPhiKhongHopLeData,
  DoiTuong,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { calculatePs } from '../../../utils/calc-util';

interface CurrencyInfo {
  ma_nt: string;
  uuid: string;
}

export const getDetailColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void,
  currencyInfo?: CurrencyInfo | null,
  exchangeRate?: number
): GridColDef[] => {
  const baseColumns: GridColDef[] = [
    {
      field: 'ma_ngvkt',
      headerName: 'Mã nghiệp vụ',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`}
          searchColumns={accountantSearchColumns}
          dialogTitle='Danh mục nghiệp vụ'
          columnDisplay='ma_ngvkt'
          value={params.row.ma_ngvkt_data?.ma_ngvkt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_ngvkt_data', row);
          }}
        />
      )
    },
    {
      field: 'ten_ngvkt',
      headerName: 'Tên nghiệp vụ',
      width: 200,
      renderCell: (params: { row: any }) => (
        <CellField name='ten_ngvkt' type='text' value={params.row.ma_ngvkt_data?.ten_ngvkt} />
      )
    },
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          columnDisplay='code'
          value={params.row.tk_no_data?.code || params.row.ma_ngvkt_data?.tk_no_data?.code || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'tk_no_data', row);
          }}
        />
      )
    },
    {
      field: 'tk_co',
      headerName: 'Tài khoản có',
      width: 200,
      renderCell: (params: { row: any }) => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          columnDisplay='code'
          value={params.row.tk_co_data?.code || params.row.ma_ngvkt_data?.tk_co_data?.code || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'tk_co_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_kh',
      headerName: 'Mã khách nợ',
      width: 200,
      renderCell: (params: { row: any }) => (
        <SearchField<DoiTuong>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
          searchColumns={khachHangSearchColumns}
          dialogTitle='Danh mục đối tượng'
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          value={params.row.ma_kh_data?.customer_code || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_kh_data', row);
          }}
        />
      )
    },
    {
      field: 'ten_kh',
      headerName: 'Tên khách hàng',
      width: 250,
      renderCell: (params: { row: any }) => (
        <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name} />
      )
    },
    {
      field: 'ma_kh_co',
      headerName: 'Mã khách có',
      width: 200,
      renderCell: (params: { row: any }) => (
        <SearchField<DoiTuong>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
          searchColumns={khachHangSearchColumns}
          dialogTitle='Danh mục đối tượng'
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          value={params.row.ma_kh_data?.customer_code || params.row.ma_kh_co_data?.customer_code || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_kh_co_data', row);
          }}
        />
      )
    },
    {
      field: 'ps_nt',
      headerName: currencyInfo?.ma_nt && currencyInfo.ma_nt !== 'VND' ? `Tiền ${currencyInfo.ma_nt}` : 'Tiền VND',
      width: 200,
      renderCell: (params: { row: any }) => (
        <CellField
          name='ps_nt'
          type='number'
          value={params.row.ps_nt}
          onValueChange={(newValue: any) => {
            // Only update ps_nt, ps will be calculated automatically in renderCell
            onCellValueChange(params.row.uuid, 'ps_nt', newValue);
          }}
        />
      )
    },
    {
      field: 'dien_giai',
      headerName: 'Diễn giải',
      width: 250,
      renderCell: (params: { row: any }) => (
        <CellField
          name='dien_giai'
          type='text'
          value={params.row.dien_giai}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
        />
      )
    },
    {
      field: 'so_ct0',
      headerName: 'Số hoá đơn',
      width: 100,
      renderCell: (params: { row: any }) => (
        <CellField
          name='so_ct0'
          type='text'
          value={params.row.so_ct0}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
        />
      )
    },
    {
      field: 'ngay_ct0',
      headerName: 'Ngày hoá đơn',
      width: 100,
      renderCell: (params: { row: any }) => (
        <CellField
          name='ngay_ct0'
          type='date'
          value={params.row.ngay_ct0}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
        />
      )
    },
    // Conditionally add ps column for non-VND currencies
    ...(currencyInfo?.ma_nt && currencyInfo.ma_nt !== 'VND'
      ? [
          {
            field: 'ps',
            headerName: 'Tiền',
            width: 200,
            renderCell: (params: { row: any }) => (
              <CellField
                name='ps'
                type='number'
                value={calculatePs(params.row.ps_nt || 0, exchangeRate || 1)}
                disabled={true} // Read-only, calculated from ps_nt * ty_gia
              />
            )
          }
        ]
      : []),
    {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<BoPhan>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
          searchColumns={boPhanSearchColumns}
          dialogTitle='Danh mục bộ phận'
          columnDisplay='ma_bp'
          displayRelatedField='ten_bp'
          value={params.row.ma_bp_data?.ma_bp || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_bp_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_vv',
      headerName: 'Vụ việc',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<VuViec>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
          searchColumns={vuViecSearchColumns}
          dialogTitle='Danh mục vụ việc'
          columnDisplay='ma_vu_viec'
          displayRelatedField='ten_vu_viec'
          value={params.row.ma_vv_data?.ma_vu_viec || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_vv_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_hd',
      headerName: 'Mã hợp đồng',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<HopDong>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
          searchColumns={hopDongSearchColumns}
          dialogTitle='Danh mục hợp đồng'
          columnDisplay='ma_hd'
          displayRelatedField='ten_hd'
          value={params.row.ma_hd_data?.ma_hd || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_hd_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_dtt',
      headerName: 'Đợt thanh toán',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<DotThanhToan>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
          searchColumns={dotThanhToanSearchColumns}
          dialogTitle='Danh mục đợt thanh toán'
          columnDisplay='ma_dtt'
          displayRelatedField='ten_dtt'
          value={params.row.ma_dtt_data?.ma_dtt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_dtt_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_ku',
      headerName: 'Khế ước',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<KheUoc>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
          searchColumns={kheUocSearchColumns}
          dialogTitle='Danh mục khế ước'
          columnDisplay='ma_ku'
          displayRelatedField='ten_ku'
          value={params.row.ma_ku_data?.ma_ku || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_ku_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_phi',
      headerName: 'Phí',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<Phi>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.PHI}`}
          searchColumns={phiSearchColumns}
          dialogTitle='Danh mục phí'
          columnDisplay='ma_phi'
          displayRelatedField='ten_phi'
          value={params.row.ma_phi_data?.ma_phi || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_phi_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 120,
      renderCell: (params: { row: any }) => (
        <SearchField<VatTu>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
          searchColumns={vatTuSearchColumns}
          dialogTitle='Danh mục sản phẩm'
          columnDisplay='ma_vt'
          displayRelatedField='ten_vt'
          value={params.row.ma_sp_data?.ma_vt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_sp_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_lsx',
      headerName: 'Lệnh sản xuất',
      width: 150,
      renderCell: (params: { row: any }) => (
        <SearchField<any>
          type='text'
          searchEndpoint={'/'}
          searchColumns={lenhSanXuatSearchColumns}
          dialogTitle='Danh mục lệnh sản xuất'
          columnDisplay='ma_lsx'
          displayRelatedField='ten_lsx'
          value={params.row.ma_lsx || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ma_lsx_data', row);
          }}
        />
      )
    },
    {
      field: 'ma_cp0',
      headerName: 'C/p không h/lệ',
      width: 180,
      renderCell: (params: { row: any }) => (
        <SearchField<ChiPhiKhongHopLeData>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
          searchColumns={chiPhiKhongHopLeSearchColumns}
          columnDisplay='ma_cp_khl'
          dialogTitle='Danh mục chi phí không hợp lệ'
          value={params.row.ma_cp0_data?.ma_cpkhl || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
        />
      )
    }
  ];

  return baseColumns;
};
