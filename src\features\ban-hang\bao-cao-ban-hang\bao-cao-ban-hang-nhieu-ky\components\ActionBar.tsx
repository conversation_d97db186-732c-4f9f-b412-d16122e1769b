import { Pin, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  reportTemplate?: string;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  reportTemplate,
  dateRange
}) => {
  // Map report template codes to names
  const getReportTemplateName = (template: string) => {
    const templateMap: { [key: string]: string } = {
      '1': 'Mẫu số lượng',
      '2': 'Mẫu doanh thu',
      '3': 'Mẫu doanh thu/lợi nhuận'
    };

    return templateMap[template] || template || 'Mẫu số lượng';
  };

  // Format date range display
  const formatDateRange = () => {
    if (!dateRange?.startDate || !dateRange?.endDate) return '';

    // Convert dates to display format
    const formatDate = (dateStr: string) => {
      try {
        // Handle different date formats
        let date: Date;

        // If it's in YYYYMMDD format (e.g., 20240804)
        if (/^\d{8}$/.test(dateStr)) {
          const year = parseInt(dateStr.substring(0, 4));
          const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
          const day = parseInt(dateStr.substring(6, 8));
          date = new Date(year, month, day);
        }
        // If it's already in DD/MM/YYYY format, parse it correctly
        else if (dateStr.includes('/')) {
          const [day, month, year] = dateStr.split('/');
          date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        // If it's in YYYY-MM-DD format
        else if (dateStr.includes('-')) {
          date = new Date(dateStr);
        }
        // If it's just a string, try to parse
        else {
          date = new Date(dateStr);
        }

        // Check if date is valid
        if (isNaN(date.getTime())) {
          return dateStr; // Return original if can't parse
        }

        return date.toLocaleDateString('vi-VN');
      } catch {
        return dateStr;
      }
    };

    return `Từ ngày ${formatDate(dateRange.startDate)} đến ngày ${formatDate(dateRange.endDate)}`;
  };

  const titleComponent = (
    <div className='flex flex-col'>
      <h1 className='text-xl font-bold'>
        Báo cáo phân tích nhiều kỳ {reportTemplate && `(${getReportTemplateName(reportTemplate)})`}
      </h1>
      {dateRange && (
        <div className='flex items-center gap-1 text-sm text-gray-600'>
          <span className='h-2 w-2 rounded-full bg-red-500'></span>
          <span>{formatDateRange()}</span>
        </div>
      )}
    </div>
  );

  return (
    <AritoActionBar className={className} titleComponent={titleComponent}>
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      {/* No print button */}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
