'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoHeaderTabs, AritoForm } from '@/components/custom/arito';
import { useAuth } from '@/contexts/auth-context';
import { formSchema, initialFormValues } from '../../schema';
import { HoverDropdown } from './HoverDropdown';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { useDetailRows } from './hooks';
import { OtherTab } from './OtherTab';
import { TyGiaTab } from './TyGiaTab';
import { handleUpdateRowFields } from '../../utils/calc-util';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { entityUnit } = useAuth();
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet || [], undefined, handleUpdateRowFields);

  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);


  const { tongTien, tongThanhToan } = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, formMode, tongTien, tongThanhToan, detailRows);
    onSubmit?.({ ...formData, unit_id: entityUnit?.uuid });
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  if (initialData && formMode === 'add') {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialFormValues}
        title={title}
        actionButtons={actionButtons}
        subTitle='Bút toán điều chỉnh giảm công nợ'
        onSubmit={handleSubmit}
        onClose={handleClose}
        schema={formSchema}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
              actionButtons={formMode !== 'add' && <HoverDropdown />}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                  loaiChungTu={state.loaiChungTu}
                />
              )
            },
            {
              id: 'ty_gia',
              label: 'Tỷ giá',
              component: <TyGiaTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} />
            }
          ]
        }
        bottomBar={
          activeTab === 'info' && <BottomBar tongTien={tongTien} tongThanhToan={tongThanhToan} ty_gia={state.tyGia} />
        }
      />

      {isConfirm && (
        <ConfirmationDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          message='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
