'use client';

import Split from 'react-split';
import { AritoColoredDot, InputTable, AritoDataTables, LoadingOverlay, DeleteDialog } from '@/components/custom/arito';
import { exportCreditAdviceDetailColumns, getChangingValueCreditAdviceColumns } from './cols-definition';
import { transformApiDataForEdit } from './utils/transform-data';
import { ActionBar, AddForm, SearchDialog } from './components';
import { useFormState, useGiayBaoCo, useRows } from '@/hooks';
import { useToast } from '@/hooks/use-toast';

export default function GiayBaoCoPage() {
  const { toast } = useToast();
  const { giayBaoCos, isLoading, addGiayBaoCo, updateGiayBaoCo, deleteGiayBaoCo, refreshGiayBaoCos } = useGiayBaoCo();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();

  console.log('🎯 Main page - selectedObj:', selectedObj);
  console.log('🎯 Main page - selectedObj?.uuid:', selectedObj?.uuid);

  // Sử dụng dữ liệu chi tiết có sẵn từ selectedObj
  const detail = selectedObj?.chi_tiet_giay_bao_co_data || [];

  console.log('📋 Main page - detail:', detail);
  console.log('📋 Main page - detail type:', typeof detail);
  console.log('📋 Main page - detail is array:', Array.isArray(detail));
  const {
    showForm,
    showSearch,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick: originalHandleCopyClick,
    handleSearch,
    handleCloseSearch
  } = useFormState();

  // Custom copy handler với logic kiểm tra và toast
  const handleCopyClick = () => {
    if (!selectedObj) {
      toast({
        title: 'Thông báo',
        description: 'Vui lòng chọn một giấy báo có để sao chép',
        variant: 'destructive'
      });
      return;
    }
    originalHandleCopyClick();
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: giayBaoCos,
      columns: getChangingValueCreditAdviceColumns(handleViewClick)
    },
    {
      name: 'Chưa ghi sổ',
      rows: giayBaoCos.filter(row => row.status === '0'),
      columns: getChangingValueCreditAdviceColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: giayBaoCos.filter(row => row.status === '3'),
      columns: getChangingValueCreditAdviceColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Đã ghi sổ',
      rows: giayBaoCos.filter(row => row.status === '5'),
      columns: getChangingValueCreditAdviceColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: giayBaoCos.filter(row => row.status === '99'),
      columns: getChangingValueCreditAdviceColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  const handleFormSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addGiayBaoCo(data);
      } else {
        await updateGiayBaoCo(selectedObj?.uuid, data);
      }
      refreshGiayBaoCos();
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className='flex h-full min-h-screen w-screen flex-col'>
      <SearchDialog openSearchDialog={showSearch} onCloseSearchDialog={handleCloseSearch} onSearch={handleSearch} />

      {showForm ? (
        <AddForm
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view')
              ? transformApiDataForEdit(selectedObj)
              : selectedObj && formMode === 'add' && isCopyMode
                ? {
                    ...transformApiDataForEdit(selectedObj),
                    so_ct: '',
                    uuid: undefined,
                    i_so_ct: undefined
                  }
                : formMode === 'add' && !isCopyMode
                  ? undefined
                  : undefined
          }
          onSubmit={handleFormSubmit}
          onClose={handleCloseForm}
        />
      ) : (
        <>
          <ActionBar
            onPrintClick={() => console.log('Print clicked')}
            onAddClick={handleAddClick}
            onEditClick={() => selectedObj && handleEditClick()}
            onDeleteClick={() => selectedObj && handleDeleteClick()}
            onCopyClick={handleCopyClick}
            onSearchClick={handleSearch}
            onRefreshClick={async () => {
              await refreshGiayBaoCos();
            }}
            onFixedColumnsClick={() => console.log('Fixed columns clicked')}
            onPrintMultipleClick={() => console.log('Print multiple clicked')}
            onExportDataClick={() => console.log('Export data clicked')}
            onDownloadExcelTemplateClick={() => console.log('Download Excel template clicked')}
            onImportFromExcelClick={() => console.log('Import from Excel clicked')}
            isDisabledView={!selectedObj}
          />

          {isLoading ? (
            <div className='flex flex-1 items-center justify-center'>
              <LoadingOverlay />
            </div>
          ) : (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={(params: any) => {
                    console.log('🖱️ Row clicked params:', params);
                    console.log('🖱️ Row data:', params.row);
                    console.log('🖱️ Row ID:', params.id);
                    console.log('🖱️ Row UUID:', params.row?.uuid);
                    handleRowClick(params);
                  }}
                  selectedRowId={selectedRowIndex || undefined}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable rows={detail} columns={exportCreditAdviceDetailColumns} mode='view' />
              </div>
            </Split>
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteGiayBaoCo}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
