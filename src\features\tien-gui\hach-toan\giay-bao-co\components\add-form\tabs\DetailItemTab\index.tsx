import { GridCellParams } from '@mui/x-data-grid';
import React from 'react';
import { SelectedCellInfo } from '../hooks/useDetailItemRows';
import { InputTable } from '@/components/custom/arito';
import { getDetailItemColumns } from './columns';
import { FormMode } from '@/types/form';
import ActionBar from './ActionBar';

interface DetailItemTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  ma_ngv?: string;
  dien_giai?: string;
}

export const DetailItemTab: React.FC<DetailItemTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange,
  ma_ngv,
  dien_giai
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailItemColumns(onCellValueChange, ma_ngv, dien_giai)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
        />
      }
    />
  );
};
