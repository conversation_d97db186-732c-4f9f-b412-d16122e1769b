import { useState, useEffect, useCallback } from 'react';
import { useHoaDonBanHang } from '@/hooks/queries/useHoaDonBanHang';

interface UseHoaDonBanHangDetailReturn {
  detail: any[];
  isLoadingDetail: boolean;
  error: string | null;
  fetchDetail: () => Promise<void>;
  clearDetail: () => void;
}

/**
 * Custom hook for managing HoaDonBanHang detail data
 *
 * This hook handles fetching detail data for a selected HoaDonBanHang
 * and provides loading states and error handling.
 */
export const useHoaDonBanHangDetail = (selectedUuid?: string): UseHoaDonBanHangDetailReturn => {
  const [detail, setDetail] = useState<any[]>([]);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getHoaDonBanHangDetail } = useHoaDonBanHang();

  const fetchDetail = useCallback(async () => {
    // Clear previous data
    setDetail([]);
    setError(null);

    if (!selectedUuid) {
      setDetail([]);
      return;
    }

    console.log('Fetching detail for UUID:', selectedUuid);
    setIsLoadingDetail(true);

    try {
      const detailData = await getHoaDonBanHangDetail(selectedUuid);
      setDetail((detailData as any).chi_tiet || []);
    } catch (error) {
      console.error('Error fetching detail:', error);
      const errorMessage = error instanceof Error ? error.message : 'Không thể tải chi tiết hóa đơn';
      setError(errorMessage);
      setDetail([]);
    } finally {
      setIsLoadingDetail(false);
    }
  }, [selectedUuid, getHoaDonBanHangDetail]);

  const clearDetail = useCallback(() => {
    setDetail([]);
    setError(null);
  }, []);

  // Auto-fetch when selectedUuid changes
  useEffect(() => {
    if (selectedUuid) {
      fetchDetail();
    } else {
      setDetail([]);
      setError(null);
    }
  }, [selectedUuid]); // Only depend on selectedUuid, not fetchDetail

  return {
    detail,
    isLoadingDetail,
    error,
    fetchDetail,
    clearDetail
  };
};
