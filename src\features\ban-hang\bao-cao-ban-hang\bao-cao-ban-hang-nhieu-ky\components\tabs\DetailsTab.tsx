import React from 'react';
import {
  khachHangSearchColumns,
  khuVucSearchColumns,
  vatTuSearchColumns,
  warehouseSearchColumns,
  loaiVatTuSearchColumns,
  QUERY_KEYS,
  nhomColumns
} from '@/constants';
import { KhachHang, KhuVuc, NhomKhachHang, VatTu, KhoHang, LoaiVatTu } from '@/types/schemas';
import { FormField, SearchField } from '@/components/custom/arito';
import { useSearchFieldIntegration } from '../../hooks';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const {
    handleCustomerCodeSelection,
    handleCustomerGroup1Selection,
    handleCustomerGroup2Selection,
    handleCustomerGroup3Selection,
    handleRegionSelection,
    handleProductCodeSelection,
    handleMaterialTypeSelection,
    handleProductGroup1Selection,
    handleProductGroup2Selection,
    handleProductGroup3Selection,
    handleWarehouseCodeSelection,
    getFormValue
  } = useSearchFieldIntegration();
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <SearchField<KhachHang>
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={khachHangSearchColumns}
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            value={getFormValue('customerCode_data')?.customer_code || ''}
            onRowSelection={handleCustomerCodeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <SearchField<NhomKhachHang>
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC1`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('customerGroup1_data')?.ma_nhom || ''}
                onRowSelection={handleCustomerGroup1Selection}
              />
              <SearchField<NhomKhachHang>
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC2`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('customerGroup2_data')?.ma_nhom || ''}
                onRowSelection={handleCustomerGroup2Selection}
              />
              <SearchField<NhomKhachHang>
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC3`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('customerGroup3_data')?.ma_nhom || ''}
                onRowSelection={handleCustomerGroup3Selection}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <SearchField<KhuVuc>
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
            searchColumns={khuVucSearchColumns}
            columnDisplay='rg_code'
            displayRelatedField='rgname'
            value={getFormValue('region_data')?.rg_code || ''}
            onRowSelection={handleRegionSelection}
          />
        </div>

        {/* 5. Mã vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <SearchField<VatTu>
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            searchColumns={vatTuSearchColumns}
            columnDisplay='ma_vt'
            displayRelatedField='ten_vt'
            value={getFormValue('productCode_data')?.ma_vt || ''}
            onRowSelection={handleProductCodeSelection}
          />
        </div>

        {/* 6. Loại vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại vật tư:</Label>
          <div className='flex items-center gap-4'>
            <SearchField<LoaiVatTu>
              searchEndpoint={`/${QUERY_KEYS.LOAI_VAT_TU}/`}
              searchColumns={loaiVatTuSearchColumns}
              columnDisplay='ma_lvt'
              displayRelatedField='ten_lvt'
              value={getFormValue('materialType_data')?.ma_lvt || ''}
              relatedFieldValue={getFormValue('materialType_data')?.ten_lvt || ''}
              onRowSelection={handleMaterialTypeSelection}
            />
            <div className='whitespace-nowrap'>
              <FormField name='ton_kho_yn' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
            </div>
          </div>
        </div>

        {/* 7. Nhóm vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <SearchField
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT1`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('productGroup1_data')?.ma_nhom || ''}
                onRowSelection={handleProductGroup1Selection}
              />
              <SearchField
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT2`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('productGroup2_data')?.ma_nhom || ''}
                onRowSelection={handleProductGroup2Selection}
              />
              <SearchField
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT3`}
                searchColumns={nhomColumns}
                columnDisplay='ma_nhom'
                value={getFormValue('productGroup3_data')?.ma_nhom || ''}
                onRowSelection={handleProductGroup3Selection}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho:</Label>
          <SearchField<KhoHang>
            searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
            searchColumns={warehouseSearchColumns}
            columnDisplay='ma_kho'
            displayRelatedField='ten_kho'
            value={getFormValue('warehouseCode_data')?.ma_kho || ''}
            onRowSelection={handleWarehouseCodeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-[400px]'>
            <FormField
              name='data_analysis_struct'
              type='select'
              options={[
                { value: '1', label: 'Báo cáo phân tích nhiều kì (Mẫu số lượng)' },
                { value: '2', label: 'Báo cáo phân tích nhiều kì (Mẫu doanh thu)' },
                { value: '3', label: 'Báo cáo phân tích nhiều kì (Mẫu doanh thu/lợi nhuận)' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
