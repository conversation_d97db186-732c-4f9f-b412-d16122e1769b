import dayjs from 'dayjs';

/**
 * T<PERSON>h toán ngày kết thúc (ngay_ct2) dựa trên ngày bắt đầu, số kỳ và loại phân tích
 *
 * V<PERSON> dụ:
 * - ngay_ct1: 1/10/2024, số kỳ: 10, phân tích theo: 'M' (tháng) => ngay_ct2: 31/10/2025
 * - ngay_ct1: 1/10/2024, số kỳ: 2, phân tích theo: '6M' (6 tháng) => ngay_ct2: 31/12/2024
 *
 * @param ngayCt1 - Ng<PERSON>y bắt đầu (format: YYYY-MM-DD hoặc DD/MM/YYYY)
 * @param soKy - Số kỳ phân tích
 * @param phanTichTheo - Loại phân tích ('D', 'W', 'M', 'Q', '6M', 'Y')
 * @returns Ngày kết thúc được format theo YYYY-MM-DD
 */
export function calculateEndDate(
  ngayCt1: string | Date,
  soKy: number,
  phanTichTheo: 'D' | 'W' | 'M' | 'Q' | '6M' | 'Y'
): string {
  if (!ngayCt1 || !soKy || soKy <= 0) {
    return '';
  }

  try {
    // Parse ngày bắt đầu
    let startDate: dayjs.Dayjs;

    if (typeof ngayCt1 === 'string') {
      // Kiểm tra format DD/MM/YYYY
      if (ngayCt1.includes('/')) {
        const [day, month, year] = ngayCt1.split('/');
        startDate = dayjs(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
      } else {
        // Format YYYY-MM-DD
        startDate = dayjs(ngayCt1);
      }
    } else {
      startDate = dayjs(ngayCt1);
    }

    if (!startDate.isValid()) {
      return '';
    }

    let endDate: dayjs.Dayjs;

    switch (phanTichTheo) {
      case 'D': // Ngày
        // Với phân tích theo ngày, ngày kết thúc = ngày bắt đầu + (số kỳ - 1) ngày
        endDate = startDate.add(soKy - 1, 'day');
        break;

      case 'W': // Tuần
        // Với phân tích theo tuần, tính đến cuối tuần cuối cùng
        endDate = startDate.add(soKy - 1, 'week').endOf('week');
        break;

      case 'M': // Tháng
        // Với phân tích theo tháng, tính đến ngày cuối cùng của kỳ tháng cuối cùng
        // Ví dụ: 5/8/2025 + 3 kỳ tháng = 4/11/2025
        // Kỳ 1: 5/8/2025-4/9/2025, Kỳ 2: 5/9/2025-4/10/2025, Kỳ 3: 5/10/2025-4/11/2025
        endDate = startDate.add(soKy, 'month').subtract(1, 'day');
        break;

      case 'Q': // Quý
        // Với phân tích theo quý, tính đến ngày cuối cùng của kỳ quý cuối cùng
        // Mỗi quý = 3 tháng
        // Ví dụ: 5/8/2025 + 3 kỳ quý = 4/5/2026
        // Kỳ 1: 5/8/2025-4/11/2025, Kỳ 2: 5/11/2025-4/2/2026, Kỳ 3: 5/2/2026-4/5/2026
        endDate = startDate.add(soKy * 3, 'month').subtract(1, 'day');
        break;

      case '6M': // 6 tháng
        // Với phân tích theo 6 tháng, tính đến cuối tháng của kỳ 6 tháng cuối cùng
        // Ví dụ: 1/8/2025 + 3 kỳ 6 tháng = cuối tháng 1/2027 = 31/1/2027
        // Kỳ 1: 1/8/2025-31/1/2026, Kỳ 2: 1/2/2026-31/7/2026, Kỳ 3: 1/8/2026-31/1/2027
        endDate = startDate.add(soKy * 6 - 1, 'month').endOf('month');
        break;

      case 'Y': // Năm
        // Với phân tích theo năm, tính đến ngày cuối cùng của kỳ năm cuối cùng
        // Ví dụ: 1/8/2025 + 3 kỳ năm = 31/7/2028
        // Kỳ 1: 1/8/2025-31/7/2026, Kỳ 2: 1/8/2026-31/7/2027, Kỳ 3: 1/8/2027-31/7/2028
        endDate = startDate.add(soKy, 'year').subtract(1, 'day');
        break;

      default:
        return '';
    }

    return endDate.format('YYYY-MM-DD');
  } catch (error) {
    console.error('Error calculating end date:', error);
    return '';
  }
}

/**
 * Tính toán ngày kết thúc và format theo DD/MM/YYYY
 * @param ngayCt1 - Ngày bắt đầu
 * @param soKy - Số kỳ phân tích
 * @param phanTichTheo - Loại phân tích
 * @returns Ngày kết thúc được format theo DD/MM/YYYY
 */
export function calculateEndDateFormatted(
  ngayCt1: string | Date,
  soKy: number,
  phanTichTheo: 'D' | 'W' | 'M' | 'Q' | '6M' | 'Y'
): string {
  const endDate = calculateEndDate(ngayCt1, soKy, phanTichTheo);
  if (!endDate) return '';

  try {
    const date = dayjs(endDate);
    return date.format('DD/MM/YYYY');
  } catch (error) {
    return '';
  }
}

/**
 * Validate input parameters cho việc tính toán ngày
 * @param ngayCt1 - Ngày bắt đầu
 * @param soKy - Số kỳ phân tích
 * @param phanTichTheo - Loại phân tích
 * @returns Object chứa thông tin validation
 */
export function validateDateCalculationInputs(
  ngayCt1: string | Date,
  soKy: number | string,
  phanTichTheo: string
): { isValid: boolean; error?: string } {
  if (!ngayCt1) {
    return { isValid: false, error: 'Ngày bắt đầu không được để trống' };
  }

  const numSoKy = typeof soKy === 'string' ? parseInt(soKy, 10) : soKy;
  if (!numSoKy || numSoKy <= 0) {
    return { isValid: false, error: 'Số kỳ phải là số dương' };
  }

  const validAnalysisTypes = ['D', 'W', 'M', 'Q', '6M', 'Y'];
  if (!validAnalysisTypes.includes(phanTichTheo)) {
    return { isValid: false, error: 'Loại phân tích không hợp lệ' };
  }

  // Validate date format
  try {
    let testDate: dayjs.Dayjs;
    if (typeof ngayCt1 === 'string') {
      if (ngayCt1.includes('/')) {
        const [day, month, year] = ngayCt1.split('/');
        testDate = dayjs(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
      } else {
        testDate = dayjs(ngayCt1);
      }
    } else {
      testDate = dayjs(ngayCt1);
    }

    if (!testDate.isValid()) {
      return { isValid: false, error: 'Ngày bắt đầu không hợp lệ' };
    }
  } catch (error) {
    return { isValid: false, error: 'Ngày bắt đầu không hợp lệ' };
  }

  return { isValid: true };
}
