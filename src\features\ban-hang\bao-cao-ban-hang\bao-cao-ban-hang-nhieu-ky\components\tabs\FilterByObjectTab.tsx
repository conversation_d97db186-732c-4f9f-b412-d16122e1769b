import React from 'react';
import {
  boPhanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  lenhSanXuatSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  QUERY_KEYS
} from '@/constants';
import { SearchField } from '@/components/custom/arito';
import { useSearchFieldIntegration } from '../../hooks';
import { Label } from '@/components/ui/label';

const FilterByObjectTab: React.FC = () => {
  const {
    handleDepartmentSelection,
    handleProjectSelection,
    handleContractSelection,
    handlePaymentPhaseSelection,
    handleAgreementSelection,
    handleFeeSelection,
    handleProductSelection,
    handleProductionOrderSelection,
    handleInvalidExpenseSelection,
    getFormValue
  } = useSearchFieldIntegration();

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bộ phận:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            searchColumns={boPhanSearchColumns}
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            value={getFormValue('department_data')?.ma_bp || ''}
            onRowSelection={handleDepartmentSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Vụ việc:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
            searchColumns={vuViecSearchColumns}
            columnDisplay='ma_vu_viec'
            displayRelatedField='ten_vu_viec'
            value={getFormValue('project_data')?.ma_vu_viec || ''}
            onRowSelection={handleProjectSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hợp đồng:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
            searchColumns={hopDongSearchColumns}
            columnDisplay='ma_hd'
            displayRelatedField='ten_hd'
            value={getFormValue('contract_data')?.ma_hd || ''}
            onRowSelection={handleContractSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đợt thanh toán:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
            searchColumns={dotThanhToanSearchColumns}
            columnDisplay='ma_dtt'
            displayRelatedField='ten_dtt'
            value={getFormValue('paymentPhase_data')?.ma_dtt || ''}
            onRowSelection={handlePaymentPhaseSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khế ước:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
            searchColumns={kheUocSearchColumns}
            columnDisplay='ma_ku'
            displayRelatedField='ten_ku'
            value={getFormValue('agreement_data')?.ma_ku || ''}
            onRowSelection={handleAgreementSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phí:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.PHI}/`}
            searchColumns={phiSearchColumns}
            columnDisplay='ma_phi'
            displayRelatedField='ten_phi'
            value={getFormValue('fee_data')?.ma_phi || ''}
            onRowSelection={handleFeeSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Sản phẩm:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            searchColumns={vatTuSearchColumns}
            columnDisplay='ma_vt'
            displayRelatedField='ten_vt'
            value={getFormValue('product_data')?.ma_vt || ''}
            onRowSelection={handleProductSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Lệnh sản xuất:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
            searchColumns={lenhSanXuatSearchColumns}
            columnDisplay='so_lsx'
            displayRelatedField='dien_giai'
            value={getFormValue('productionOrder_data')?.so_lsx || ''}
            onRowSelection={handleProductionOrderSelection}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>C/p không h/lệ:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
            searchColumns={chiPhiKhongHopLeSearchColumns}
            columnDisplay='ma_cpkhl'
            displayRelatedField='ten_cpkhl'
            value={getFormValue('invalidExpense_data')?.ma_cpkhl || ''}
            onRowSelection={handleInvalidExpenseSelection}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterByObjectTab;
