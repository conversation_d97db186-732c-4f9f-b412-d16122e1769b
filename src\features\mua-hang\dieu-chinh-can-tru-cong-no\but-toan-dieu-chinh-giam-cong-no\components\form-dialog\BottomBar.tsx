import { FormField } from '@/components/custom/arito';
import { formatMoney } from '@/lib/formatUtils';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  tongTien: number;
  tongThanhToan: number;
  ty_gia: number;
}

export function BottomBar({ tongTien, tongThanhToan, ty_gia }: BottomBarProps) {
  const isVND = ty_gia === 1;

  return (
    <div className='w-full border-t bg-white px-4 py-2 pb-6'>
      <div className='flex'>
        <div className='flex flex-col'>
          <div className='flex items-center gap-4'>
            <Label className='mt-2 w-28 font-bold'>Tổng tiền</Label>
            <FormField name='t_tien_nt' type='text' value={formatMoney(tongTien)} />
          </div>
          <div className='flex items-center gap-4'>
            <Label className='mt-2 w-28 font-bold'>Tổng thanh toán</Label>
            <FormField name='t_tt_nt' type='text' value={formatMoney(tongThanhToan)} />
          </div>
        </div>
      </div>
    </div>
  );
}
