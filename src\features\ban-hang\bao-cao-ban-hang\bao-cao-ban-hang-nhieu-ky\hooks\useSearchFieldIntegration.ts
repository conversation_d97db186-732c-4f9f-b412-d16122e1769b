import { useFormContext } from 'react-hook-form';
import { useSearchFieldStates } from './useSearchFieldStates';

/**
 * Hook that integrates useSearchFieldStates with react-hook-form
 * Provides handlers for SearchField onRowSelection that automatically
 * update both the search field state and form values
 */
export function useSearchFieldIntegration() {
  const { setValue, getValues } = useFormContext();
  const searchFieldStates = useSearchFieldStates();

  // DetailsTab handlers
  const handleCustomerCodeSelection = (row: any) => {
    searchFieldStates.setCustomerCode(row);
    setValue('customerCode', row.uuid);
    setValue('customerCode_data', row);
    setValue('ma_kh', row.uuid);
  };

  const handleCustomerGroup1Selection = (row: any) => {
    searchFieldStates.setCustomerGroup1(row);
    setValue('customerGroup1', row.uuid);
    setValue('customerGroup1_data', row);
    setValue('nh_kh1', row.uuid);
  };

  const handleCustomerGroup2Selection = (row: any) => {
    searchFieldStates.setCustomerGroup2(row);
    setValue('customerGroup2', row.uuid);
    setValue('customerGroup2_data', row);
  };

  const handleCustomerGroup3Selection = (row: any) => {
    searchFieldStates.setCustomerGroup3(row);
    setValue('customerGroup3', row.uuid);
    setValue('customerGroup3_data', row);
  };

  const handleRegionSelection = (row: any) => {
    searchFieldStates.setRegion(row);
    setValue('region', row.uuid);
    setValue('region_data', row);
  };

  const handleProductCodeSelection = (row: any) => {
    searchFieldStates.setProductCode(row);
    setValue('productCode', row.uuid);
    setValue('productCode_data', row);
    setValue('ma_vt', row.uuid);
  };

  const handleMaterialTypeSelection = (row: any) => {
    searchFieldStates.setMaterialType(row);
    setValue('materialType', row.uuid);
    setValue('materialType_data', row);
    setValue('ma_lvt', row.uuid);
  };

  const handleProductGroup1Selection = (row: any) => {
    searchFieldStates.setProductGroup1(row);
    setValue('productGroup1', row.uuid);
    setValue('productGroup1_data', row);
    setValue('nh_vt1', row.uuid);
  };

  const handleProductGroup2Selection = (row: any) => {
    searchFieldStates.setProductGroup2(row);
    setValue('productGroup2', row.uuid);
    setValue('productGroup2_data', row);
  };

  const handleProductGroup3Selection = (row: any) => {
    searchFieldStates.setProductGroup3(row);
    setValue('productGroup3', row.uuid);
    setValue('productGroup3_data', row);
  };

  const handleWarehouseCodeSelection = (row: any) => {
    searchFieldStates.setWarehouseCode(row);
    setValue('warehouseCode', row.uuid);
    setValue('warehouseCode_data', row);
  };

  // FilterByObjectTab handlers
  const handleDepartmentSelection = (row: any) => {
    searchFieldStates.setDepartment(row);
    setValue('department', row.uuid);
    setValue('department_data', row);
    setValue('ma_bp', row.uuid);
  };

  const handleProjectSelection = (row: any) => {
    searchFieldStates.setProject(row);
    setValue('project', row.uuid);
    setValue('project_data', row);
  };

  const handleContractSelection = (row: any) => {
    searchFieldStates.setContract(row);
    setValue('contract', row.uuid);
    setValue('contract_data', row);
  };

  const handlePaymentPhaseSelection = (row: any) => {
    searchFieldStates.setPaymentPhase(row);
    setValue('paymentPhase', row.uuid);
    setValue('paymentPhase_data', row);
  };

  const handleAgreementSelection = (row: any) => {
    searchFieldStates.setAgreement(row);
    setValue('agreement', row.uuid);
    setValue('agreement_data', row);
  };

  const handleFeeSelection = (row: any) => {
    searchFieldStates.setFee(row);
    setValue('fee', row.uuid);
    setValue('fee_data', row);
  };

  const handleProductSelection = (row: any) => {
    searchFieldStates.setProduct(row);
    setValue('product', row.uuid);
    setValue('product_data', row);
  };

  const handleProductionOrderSelection = (row: any) => {
    searchFieldStates.setProductionOrder(row);
    setValue('productionOrder', row.uuid);
    setValue('productionOrder_data', row);
  };

  const handleInvalidExpenseSelection = (row: any) => {
    searchFieldStates.setInvalidExpense(row);
    setValue('invalidExpense', row.uuid);
    setValue('invalidExpense_data', row);
  };

  // OtherTab handlers
  const handleTransactionCodeSelection = (row: any) => {
    searchFieldStates.setTransactionCode(row);
    setValue('transactionCode', row.uuid);
    setValue('transactionCode_data', row);
  };

  const handleItemAccountSelection = (row: any) => {
    searchFieldStates.setItemAccount(row);
    setValue('itemAccount', row.uuid);
    setValue('itemAccount_data', row);
  };

  const handleEmployeeCodeSelection = (row: any) => {
    searchFieldStates.setEmployeeCode(row);
    setValue('employeeCode', row.uuid);
    setValue('employeeCode_data', row);
  };

  const handleRevenueAccountSelection = (row: any) => {
    searchFieldStates.setRevenueAccount(row);
    setValue('revenueAccount', row.uuid);
    setValue('revenueAccount_data', row);
  };

  const handleCostAccountSelection = (row: any) => {
    searchFieldStates.setCostAccount(row);
    setValue('costAccount', row.uuid);
    setValue('costAccount_data', row);
  };

  const handleBatchCodeSelection = (row: any) => {
    searchFieldStates.setBatchCode(row);
    setValue('batchCode', row.uuid);
    setValue('batchCode_data', row);
  };

  const handleLocationCodeSelection = (row: any) => {
    searchFieldStates.setLocationCode(row);
    setValue('locationCode', row.uuid);
    setValue('locationCode_data', row);
  };

  return {
    // Expose all search field states
    ...searchFieldStates,

    // DetailsTab handlers
    handleCustomerCodeSelection,
    handleCustomerGroup1Selection,
    handleCustomerGroup2Selection,
    handleCustomerGroup3Selection,
    handleRegionSelection,
    handleProductCodeSelection,
    handleMaterialTypeSelection,
    handleProductGroup1Selection,
    handleProductGroup2Selection,
    handleProductGroup3Selection,
    handleWarehouseCodeSelection,

    // FilterByObjectTab handlers
    handleDepartmentSelection,
    handleProjectSelection,
    handleContractSelection,
    handlePaymentPhaseSelection,
    handleAgreementSelection,
    handleFeeSelection,
    handleProductSelection,
    handleProductionOrderSelection,
    handleInvalidExpenseSelection,

    // OtherTab handlers
    handleTransactionCodeSelection,
    handleItemAccountSelection,
    handleEmployeeCodeSelection,
    handleRevenueAccountSelection,
    handleCostAccountSelection,
    handleBatchCodeSelection,
    handleLocationCodeSelection,

    // Helper function to get current form values for display
    getFormValue: (fieldName: string) => getValues(fieldName)
  };
}
