import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';

export interface DetailRow {
  uuid?: string | null;
  so_luong?: number;
  gia_nt2?: number;
  tien_nt2?: number;
  gia_nt?: number;
  tien_nt?: number;
  thue_nt?: number;
  px_dd?: boolean;
  ma_vt_data?: {
    gia_ton?: number;
    [key: string]: any;
  };
  ma_thue_data?: {
    thue_suat?: number;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useDetailRows(initialRows: DetailRow[] = []) {
  const [rows, setRows] = useState<DetailRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<DetailRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleAddRow = () => {
    const newRow = {
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: DetailRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    // No need to update selectedRowUuid as it remains the same
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    // Auto-calculate when price (gia_nt2) changes
    if (field === 'gia_nt2') {
      const so_luong = Number(updatedRows[rowIndex].so_luong) || 1;
      const gia = Number(newValue) || 0;
      const tien = gia * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt2: tien
      };
    }
    // Auto-calculate when price (gia_nt) changes
    else if (field === 'gia_nt') {
      const so_luong = Number(updatedRows[rowIndex].so_luong) || 1;
      const gia = Number(newValue) || 0;
      const tien = gia * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt: tien
      };
    }
    // Auto-calculate when quantity (so_luong) changes
    else if (field === 'so_luong') {
      const gia_nt2 = Number(updatedRows[rowIndex].gia_nt2) || 0;
      const gia_nt = Number(updatedRows[rowIndex].gia_nt) || Number(updatedRows[rowIndex].ma_vt_data?.gia_ton) || 0;
      const so_luong = Number(newValue) || 1;
      const tien_nt2 = gia_nt2 * so_luong;
      const tien_nt = gia_nt * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt2: tien_nt2,
        tien_nt: tien_nt
      };
    }
    // Auto-set gia_nt and calculate tien_nt when ma_vt_data changes
    else if (field === 'ma_vt_data') {
      const gia_ton = Number(newValue?.gia_ton) || 0;
      const so_luong = Number(updatedRows[rowIndex].so_luong) || 1;
      const tien_nt = gia_ton * so_luong;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        gia_nt: gia_ton,
        tien_nt: tien_nt
      };
    }
    // Auto-set gia_nt when px_dd checkbox changes
    else if (field === 'px_dd') {
      if (newValue && updatedRows[rowIndex].ma_vt_data?.gia_ton) {
        const gia_ton = Number(updatedRows[rowIndex].ma_vt_data.gia_ton) || 0;
        const so_luong = Number(updatedRows[rowIndex].so_luong) || 1;
        const tien_nt = gia_ton * so_luong;

        updatedRows[rowIndex] = {
          ...updatedRows[rowIndex],
          gia_nt: gia_ton,
          tien_nt: tien_nt
        };
      }
    }

    // Auto-calculate tax when tax rate (ma_thue_data) changes
    if (field === 'ma_thue_data') {
      const thue_suat = Number(updatedRows[rowIndex].ma_thue_data?.thue_suat) || 0;
      const tien = Number(updatedRows[rowIndex].tien_nt2) || 0;
      const thue = (tien * thue_suat) / 100;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        thue_nt: thue
      };
    }

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: DetailRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as DetailRow);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
}
