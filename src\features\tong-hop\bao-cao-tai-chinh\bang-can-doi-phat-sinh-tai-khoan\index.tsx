'use client';

import React, { useEffect } from 'react';
import { ActionBar, SearchDialog, EditPrintTemplateDialog } from './components';
import { useRowSelection, useDialogState, useTrialBalanceData } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { TrialBalanceColumns } from './cols-definition';
import { initialSearchValues } from './schema';

export default function BangCanDoiPhatSinhTaiKhoan({ initialRows }: { initialRows: any[] }) {
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    initialSearchDialogOpen,
    showEditPrintDialog,
    showData,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    openEditPrintDialog,
    closeEditPrintDialog,

    handleViewBookButtonClick,
    handleRefreshButtonClick,
    handleFixedColumnsButtonClick,
    handleEditPrintButtonClick,
    handleExportButtonClick
  } = useDialogState(clearSelection);

  // Use trial balance data hook
  const { data: trialBalanceData, isLoading, error, fetchData } = useTrialBalanceData(searchParams || {});

  // Call fetchData when searchParams change (only when user submits form)
  useEffect(() => {
    if (searchParams) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  const tables = [
    {
      name: '',
      rows: trialBalanceData,
      columns: TrialBalanceColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <SearchDialog
        openSearchDialog={initialSearchDialogOpen}
        onCloseSearchDialog={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={showEditPrintDialog}
        onClose={closeEditPrintDialog}
        onSave={handleEditPrintButtonClick}
      />

      {showData && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onViewBookClick={handleViewBookButtonClick}
            onRefreshClick={handleRefreshButtonClick}
            onFixedColumnsClick={handleFixedColumnsButtonClick}
            onExportClick={handleExportButtonClick}
            onEditPrintClick={handleEditPrintButtonClick}
            searchParams={searchParams}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
