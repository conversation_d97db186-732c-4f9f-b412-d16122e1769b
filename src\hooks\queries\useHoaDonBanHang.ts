import { useState, useEffect } from 'react';
import { HoaDonBanHang, HoaDonBanHangInput, HoaDonBanHangResponse } from '@/types/schemas/hoa-don-ban-hang.type';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseHoaDonBanHangReturn {
  hoaDonBanHangs: HoaDonBanHang[];
  isLoading: boolean;
  isLoadingDetail: boolean;
  addHoaDonBanHang: (newHoaDonBanHang: HoaDonBanHangInput) => Promise<HoaDonBanHang>;
  updateHoaDonBanHang: (uuid: string, updatedHoaDonBanHang: HoaDonBanHangInput) => Promise<HoaDonBanHang>;
  deleteHoaDonBanHang: (uuid: string) => Promise<void>;
  refreshHoaDonBanHangs: () => Promise<void>;
  getHoaDonBanHangByCustomer: (customerId: string) => Promise<HoaDonBanHang[]>;
  getHoaDonBanHangDetail: (uuid: string) => Promise<[]>;
}

/**
 * Hook for managing HoaDonBanHang (Sales Invoice) data
 *
 * This hook provides functions to fetch, create, update, and delete sales invoices and their details.
 */
export const useHoaDonBanHang = (initialHoaDonBanHangs: HoaDonBanHang[] = []): UseHoaDonBanHangReturn => {
  const [hoaDonBanHangs, setHoaDonBanHangs] = useState<HoaDonBanHang[]>(initialHoaDonBanHangs);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchHoaDonBanHangs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/`
      );
      setHoaDonBanHangs(response.data.results);
    } catch (error) {
      console.error('Error fetching sales invoices:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getHoaDonBanHangByCustomer = async (customerId: string): Promise<HoaDonBanHang[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<HoaDonBanHangResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching sales invoices by customer:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addHoaDonBanHang = async (newHoaDonBanHang: HoaDonBanHangInput): Promise<HoaDonBanHang> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.post<HoaDonBanHang>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/`,
        newHoaDonBanHang
      );

      const addedHoaDonBanHang = response.data;
      setHoaDonBanHangs(prev => [...prev, addedHoaDonBanHang]);
      return addedHoaDonBanHang;
    } catch (error) {
      console.error('Error adding sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateHoaDonBanHang = async (
    uuid: string,
    updatedHoaDonBanHang: HoaDonBanHangInput
  ): Promise<HoaDonBanHang> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.put<HoaDonBanHang>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/${uuid}/`,
        updatedHoaDonBanHang
      );

      const updatedData = response.data;
      setHoaDonBanHangs(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
      return updatedData;
    } catch (error) {
      console.error('Error updating sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteHoaDonBanHang = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/${uuid}/`);
      setHoaDonBanHangs(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getHoaDonBanHangDetail = async (uuid: string): Promise<[]> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.get<[]>(`/entities/${entity.slug}/erp/${QUERY_KEYS.HOA_DON_BAN_HANG}/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching sales invoice detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  useEffect(() => {
    fetchHoaDonBanHangs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    hoaDonBanHangs,
    isLoading,
    isLoadingDetail,
    addHoaDonBanHang,
    updateHoaDonBanHang,
    deleteHoaDonBanHang,
    refreshHoaDonBanHangs: fetchHoaDonBanHangs,
    getHoaDonBanHangByCustomer,
    getHoaDonBanHangDetail
  };
};
