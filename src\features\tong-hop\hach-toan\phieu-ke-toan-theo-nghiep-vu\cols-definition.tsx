import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const materialStandardColumns: GridColDef[] = [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Chưa ghi số';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi số';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250
  },
  {
    field: 't_ps_nt',
    headerName: 'Tổng phát sinh',
    width: 120
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  }
];

export const inputTableColumns: GridColDef[] = [
  {
    field: 'ma_ngvkt',
    headerName: 'Mã nghiệp vụ',
    renderCell: params => params.row.ma_ngvkt_data?.ma_ngvkt,
    width: 120
  },
  {
    field: 'ten_ngvkt',
    headerName: 'Tên nghiệp vụ',
    renderCell: params => params.row.ma_ngvkt_data?.ten_ngvkt,
    width: 250
  },
  { field: 'tk_no', headerName: 'Tài khoản nợ', renderCell: params => params.row.tk_no_data?.code, width: 120 },
  { field: 'tk_co', headerName: 'Tài khoản có', renderCell: params => params.row.tk_co_data?.code, width: 120 },
  { field: 'ma_kh', headerName: 'Mã khách nợ', renderCell: params => params.row.ma_kh_data?.customer_code, width: 120 },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    renderCell: params => params.row.ma_kh_data?.customer_name,
    width: 250
  },
  {
    field: 'ma_kh_co',
    headerName: 'Mã khách có',
    renderCell: params => params.row.ma_kh_co_data?.customer_code,
    width: 120
  },
  { field: 'ps_nt', headerName: 'Tiền %s', width: 120 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'so_ct0', headerName: 'Số hoá đơn', width: 120 },
  { field: 'ngay_ct0', headerName: 'Ngày hoá đơn', width: 120 },
  { field: 'ps', headerName: 'Tiền', width: 120 },
  { field: 'ma_bp', headerName: 'Bộ phận', renderCell: params => params.row.ma_bp_data?.ten_bp, width: 120 },
  { field: 'ma_vv', headerName: 'Vụ việc', renderCell: params => params.row.ma_vv_data?.ma_vu_viec, width: 120 },
  { field: 'ma_hd', headerName: 'Hợp đồng', renderCell: params => params.row.ma_hd_data?.ma_hd, width: 120 },
  { field: 'ma_dtt', headerName: 'Đợt thanh toán', renderCell: params => params.row.ma_dtt_data?.ten_dtt, width: 120 },
  { field: 'ma_ku', headerName: 'Khế ước', renderCell: params => params.row.ma_ku_data?.ten_ku, width: 120 },
  { field: 'ma_phi', headerName: 'Phí', renderCell: params => params.row.ma_phi_data?.ten_phi, width: 120 },
  { field: 'ma_sp', headerName: 'Sản phẩm', renderCell: params => params.row.ma_sp_data?.ten_vt, width: 120 },
  { field: 'ma_lsx', headerName: 'Lệnh sản xuất', renderCell: params => params.row.ma_lsx_data?.ma_lsx, width: 120 },
  { field: 'ma_cp0', headerName: 'C/p không h/lệ', renderCell: params => params.row.ma_cp0_data?.ma_cpkhl, width: 120 }
];
