import { useState, useCallback } from 'react';

/**
 * Hook for managing search field states in the bao-cao-ban-hang-nhieu-ky feature
 *
 * This hook manages all SearchField states that are not handled by AritoForm directly.
 * SearchField components use onRowSelection to update these states.
 */
export function useSearchFieldStates() {
  // DetailsTab search fields
  const [customerCode, setCustomerCode] = useState<any>(null);
  const [customerGroup1, setCustomerGroup1] = useState<any>(null);
  const [customerGroup2, setCustomerGroup2] = useState<any>(null);
  const [customerGroup3, setCustomerGroup3] = useState<any>(null);
  const [region, setRegion] = useState<any>(null);
  const [productCode, setProductCode] = useState<any>(null);
  const [materialType, setMaterialType] = useState<any>(null);
  const [productGroup1, setProductGroup1] = useState<any>(null);
  const [productGroup2, setProductGroup2] = useState<any>(null);
  const [productGroup3, setProductGroup3] = useState<any>(null);
  const [warehouseCode, setWarehouseCode] = useState<any>(null);

  // FilterByObjectTab search fields
  const [department, setDepartment] = useState<any>(null);
  const [project, setProject] = useState<any>(null);
  const [contract, setContract] = useState<any>(null);
  const [paymentPhase, setPaymentPhase] = useState<any>(null);
  const [agreement, setAgreement] = useState<any>(null);
  const [fee, setFee] = useState<any>(null);
  const [product, setProduct] = useState<any>(null);
  const [productionOrder, setProductionOrder] = useState<any>(null);
  const [invalidExpense, setInvalidExpense] = useState<any>(null);

  // OtherTab search fields
  const [transactionCode, setTransactionCode] = useState<any>(null);
  const [itemAccount, setItemAccount] = useState<any>(null);
  const [employeeCode, setEmployeeCode] = useState<any>(null);
  const [revenueAccount, setRevenueAccount] = useState<any>(null);
  const [costAccount, setCostAccount] = useState<any>(null);
  const [batchCode, setBatchCode] = useState<any>(null);
  const [locationCode, setLocationCode] = useState<any>(null);

  const resetSearchFields = useCallback(() => {
    // Reset DetailsTab fields
    setCustomerCode(null);
    setCustomerGroup1(null);
    setCustomerGroup2(null);
    setCustomerGroup3(null);
    setRegion(null);
    setProductCode(null);
    setMaterialType(null);
    setProductGroup1(null);
    setProductGroup2(null);
    setProductGroup3(null);
    setWarehouseCode(null);

    // Reset FilterByObjectTab fields
    setDepartment(null);
    setProject(null);
    setContract(null);
    setPaymentPhase(null);
    setAgreement(null);
    setFee(null);
    setProduct(null);
    setProductionOrder(null);
    setInvalidExpense(null);

    // Reset OtherTab fields
    setTransactionCode(null);
    setItemAccount(null);
    setEmployeeCode(null);
    setRevenueAccount(null);
    setCostAccount(null);
    setBatchCode(null);
    setLocationCode(null);
  }, []);

  const getSearchFieldData = useCallback(() => {
    return {
      // DetailsTab - Customer fields (API field names from state variables)
      ma_kh: customerCode?.uuid || null,
      nh_kh1: customerGroup1?.uuid || null,
      nh_kh2: customerGroup2?.uuid || null,
      nh_kh3: customerGroup3?.uuid || null,
      rg_code: region?.uuid || null,

      // DetailsTab - Product fields (API field names from state variables)
      ma_vt: productCode?.uuid || null,
      ma_lvt: materialType?.uuid || null, // Loại vật tư - now mapped to SearchField
      ton_kho_yn: false, // Theo dõi tồn kho - boolean field
      nh_vt1: productGroup1?.uuid || null,
      nh_vt2: productGroup2?.uuid || null,
      nh_vt3: productGroup3?.uuid || null,
      ma_kho: warehouseCode?.uuid || null,
      ma_unit: null, // Mã đơn vị - not mapped to SearchField yet
      nh_ct: null, // Nhóm chứng từ - not mapped to SearchField yet

      // FilterByObjectTab fields (API field names from state variables)
      ma_bp: department?.uuid || null,
      ma_vv: project?.uuid || null,
      ma_hd: contract?.uuid || null,
      ma_dtt: paymentPhase?.uuid || null,
      ma_ku: agreement?.uuid || null,
      ma_phi: fee?.uuid || null,
      ma_sp: product?.uuid || null,
      ma_lsx: productionOrder?.uuid || null,
      ma_cp0: invalidExpense?.uuid || null,

      // OtherTab fields (API field names from state variables)
      ma_gd: transactionCode?.uuid || null,
      tk_vt: itemAccount?.uuid || null,
      ma_nvbh: employeeCode?.uuid || null,
      tk_dt: revenueAccount?.uuid || null,
      tk_gv: costAccount?.uuid || null,
      ma_lo: batchCode?.uuid || null,
      ma_vi_tri: locationCode?.uuid || null,
      so_ct1: null, // Số chứng từ từ - text field, not SearchField
      so_ct2: null, // Số chứng từ đến - text field, not SearchField
      dien_giai: null // Diễn giải - text field, not SearchField
    };
  }, [
    // DetailsTab dependencies
    customerCode,
    customerGroup1,
    customerGroup2,
    customerGroup3,
    region,
    productCode,
    materialType,
    productGroup1,
    productGroup2,
    productGroup3,
    warehouseCode,

    // FilterByObjectTab dependencies
    department,
    project,
    contract,
    paymentPhase,
    agreement,
    fee,
    product,
    productionOrder,
    invalidExpense,

    // OtherTab dependencies
    transactionCode,
    itemAccount,
    employeeCode,
    revenueAccount,
    costAccount,
    batchCode,
    locationCode
  ]);

  return {
    // DetailsTab states and setters
    customerCode,
    setCustomerCode,
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,
    region,
    setRegion,
    productCode,
    setProductCode,
    materialType,
    setMaterialType,
    productGroup1,
    setProductGroup1,
    productGroup2,
    setProductGroup2,
    productGroup3,
    setProductGroup3,
    warehouseCode,
    setWarehouseCode,

    // FilterByObjectTab states and setters
    department,
    setDepartment,
    project,
    setProject,
    contract,
    setContract,
    paymentPhase,
    setPaymentPhase,
    agreement,
    setAgreement,
    fee,
    setFee,
    product,
    setProduct,
    productionOrder,
    setProductionOrder,
    invalidExpense,
    setInvalidExpense,

    // OtherTab states and setters
    transactionCode,
    setTransactionCode,
    itemAccount,
    setItemAccount,
    employeeCode,
    setEmployeeCode,
    revenueAccount,
    setRevenueAccount,
    costAccount,
    setCostAccount,
    batchCode,
    setBatchCode,
    locationCode,
    setLocationCode,

    // Utility functions
    resetSearchFields,
    getSearchFieldData
  };
}
