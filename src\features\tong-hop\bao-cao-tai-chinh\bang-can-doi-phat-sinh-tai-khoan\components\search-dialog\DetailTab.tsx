import { SearchFieldStates } from '../../hooks/useSearchFieldStates';
import { SearchField, FormField } from '@/components/custom/arito';
import { accountSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates: SearchFieldStates;
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode, searchFieldStates }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <label className='min-w-40 text-left text-sm'>Tài kho<PERSON>n</label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={searchFieldStates?.account?.code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setAccount(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bậc tk</Label>
          <div className='w-[130px]'>
            <FormField type='number' name='bac_tk' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='loai_tk'
              disabled={formMode === 'view'}
              options={[
                { label: 'Tất cả', value: 0 },
                { label: 'Tài khoản chi tiết', value: 1 }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bù trừ số dư</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='bu_tru'
              disabled={formMode === 'view'}
              options={[
                { label: 'Không', value: 0 },
                { label: 'Có - Ngoài trừ tài khoản công nợ', value: 1 },
                { label: 'Có - Tất cả tài khoản', value: 2 }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Người lập</Label>
          <div className='w-full'>
            <FormField type='text' name='nguoi_lap' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: 20 },
                { label: 'Mẫu ngoại tệ', value: 30 }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
