import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
}

export function OtherTab({ formMode }: OtherTabProps) {
  return (
    <div className='p-4'>
      <div className='flex items-center'>
        <FileSelectField
          label='Chọn file'
          formMode={formMode}
          labelClassName='w-32 font-medium'
          onFileChange={file => console.log(file)}
        />
      </div>
    </div>
  );
}
