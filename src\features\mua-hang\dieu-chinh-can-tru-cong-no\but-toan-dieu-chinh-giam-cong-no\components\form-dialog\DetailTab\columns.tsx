import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  khachHangSearchColumns,
  hoaDonSearchColumns,
  chiPhiKhongHopLeSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  DotThanhToan,
  HopDong,
  KhachHang,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  ChiPhiKhongHopLeData
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void,
  loaiChungTu: string,
  dien_giai: string | null
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: params => (
      <CellField
        name='du_cn'
        type='number'
        value={params.row.du_cn}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'du_cn', newValue)}
      />
    )
  },
  ...(loaiChungTu === '1'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hoá đơn',
          width: 180,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${params.row.ma_kh_data?.uuid}&context=BTDCGCN-MH`}
              searchColumns={hoaDonSearchColumns}
              columnDisplay='so_ct'
              dialogTitle='Hoá đơn'
              disabled={!params.row.ma_kh_data?.uuid}
              value={params.row.id_hd_data?.so_ct || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'id_hd_data', row)}
            />
          )
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hoá đơn',
          width: 120,
          renderCell: (params: any) => <CellField name='so_ct0_hd' type='text' value={params.row.id_hd_data?.so_hd} />
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hoá đơn',
          width: 160,
          renderCell: (params: any) => (
            <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_hd} />
          )
        },
        {
          field: 'tk_no',
          headerName: 'Tài khoản nợ',
          width: 100,
          renderCell: (params: any) => params.row.id_hd_data?.tk_data?.tk
        },
        {
          field: 'ma_ngt_hd',
          headerName: 'Ngoại tệ',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='ma_ngt_hd' type='text' value={params.row.id_hd_data?.ngoai_te} />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 120,
          renderCell: (params: any) => <CellField name='ty_gia_hd' type='number' value={params.row.ty_gia_hd || 1} />
        },
        {
          field: 'tien_tren_hd',
          headerName: 'Tiền trên hóa đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='tien_tren_hd' type='number' value={params.row.id_hd_data?.tien_tren_hd || 0} />
          )
        },
        {
          field: 'da_pb',
          headerName: 'Đã phân bổ',
          width: 120
        },
        {
          field: 'con_lai',
          headerName: 'Còn lại',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='da_pb_nt' type='number' value={params.row.id_hd_data?.tien_con_phai_tt} />
          )
        }
      ]
    : []),
  ...(loaiChungTu === '2'
    ? [
        {
          field: 'tk_no',
          headerName: 'Tài khoản nợ',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_no_data?.code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
            />
          )
        },
        {
          field: 'ten_tk',
          headerName: 'Tên tài khoản',
          width: 200,
          renderCell: (params: any) => <CellField name='tk_no' type='text' value={params.row.tk_no_data?.code || ''} />
        }
      ]
    : []),
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || dien_giai || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
